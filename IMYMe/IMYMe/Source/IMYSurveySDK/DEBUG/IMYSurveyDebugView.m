//
//  IMYSurveyDebugView.m
//  IMYMe
//
//  Created by HBQ on 2024/10/17.
//

#ifdef DEBUG

#import "IMYSurveyDebugView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYPopPlanManager.h"
#import "IMYSurveySDK.h"
#import <IMYAccount/IMYAccount.h>
#import <IMYPopplanV2Manager.h>
#import "IMYPopupPlanView.h"

@interface IMYSurveyDebugView () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) NSMutableArray *datas;

@property (nonatomic, strong) UIButton *dismissButton;

@property (nonatomic, strong) UIButton *miniButton;

@property (nonatomic, strong) UIButton *cButton;

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) IMYPopplanV2PlanModel *planModel;

@end

@implementation IMYSurveyDebugView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        // 背景色
        self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
        
        // 添加拖拽手势，方便调试时移动位置
        UIPanGestureRecognizer *pan = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePan:)];
        [self addGestureRecognizer:pan];

        // setup
        [self setupDatas];
        [self setupUI];
        
        @weakify(self);
        [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"com.imyme.surveydebugview.reloadData" object:nil] deliverOnMainThread] subscribeNext:^(NSNotification * _Nullable x) {
            @strongify(self);
            [self setupDatas];
            [self.tableView reloadData];
        }];
    }
    return self;
}

// MARK: - Setup

- (void)setupDatas {
    @weakify(self);
    
    self.datas = [NSMutableArray array];
    
    //[self addDataWithTitle:@"[bizChat][商家私聊]" selector:@selector(da_bizChat_openBizChat:)];
    
    [self addDataWithTitle:@"[ppp2][查看][日志]" selector:@selector(da_ppp2_lookLog:)];
    [self addDataWithTitle:@"[ppp2][查看][曝光信息]" selector:@selector(da_ppp2_lookExpinfo:)];
    [self addDataWithTitle:@"[ppp2][查看][planList]" selector:@selector(da_ppp2_lookPlanList:)];
    [self addDataWithTitle:@"[ppp2][查看][开屏缓存]" selector:@selector(da_ppp2_lookLaunchCache:)];
    [self addDataWithTitle:@"[ppp2][查看][开屏轮播索引]" selector:@selector(da_ppp2_lookLaunchCache_index:)];
    [self addDataWithTitle:@"[ppp2][重置][popplanManager]" selector:@selector(da_ppp2_resetPopplanManager:)];
    [self addDataWithTitle:@"[ppp2][重置][开屏缓存]" selector:@selector(da_ppp2_resetLaunchCache:)];
    [self addDataWithTitle:@"[ppp2][重置][开屏轮播索引]" selector:@selector(da_ppp2_resetItem4Index:)];
    
    [self addDataWithTitle:@"[首页二楼][查看][日志]" selector:@selector(da_home2ndfloor_log:)];
    [self addDataWithTitle:@"[首页二楼][清空][日志]" selector:@selector(da_home2ndfloor_log_clean:)];
    [self addDataWithTitle:@"[首页二楼][清空][上次显示时间]" selector:@selector(da_home2ndfloor_cleanGuideLastShowTime:)];
    [self addDataWithTitle:@"[首页二楼][设置][刷新控件 debug]" selector:@selector(da_home2ndfloor_debugRefreshHeader:)];
    [self addDataWithTitle:@"[首页二楼][设置][引导动画 debug]" selector:@selector(da_home2ndfloor_guide:)];
    
    [self addDataWithTitle:@"[survey][查看][问卷日志]" selector:@selector(da_survey_lookLog:)];
    [self addDataWithTitle:@"[survey][查看][showInfos]" selector:@selector(da_survey_lookShowInfo:)];
    [self addDataWithTitle:@"[survey][查看][planList]" selector:@selector(da_survey_lookPlanList:)];
    [self addDataWithTitle:@"[survey][查看][gfc]" selector:@selector(da_survey_lookGfc:)];
    [self addDataWithTitle:@"[survey][查看][surveyList]" selector:@selector(da_survey_lookSurveyList:)];
    [self addDataWithTitle:@"[survey][查看][lastShowTime]" selector:@selector(da_survey_lookLastShowTime:)];
    [self addDataWithTitle:@"[survey][重置][问卷日志]" selector:@selector(da_survey_resetLog:)];
    [self addDataWithTitle:@"[survey][重置][清空所有缓存]" selector:@selector(da_survey_reset:)];
    
    [self addDataWithTitle:@"[我页面引导][清空][个人主页 1202]" selector:@selector(da_jindouCover_clearHomePage:)];
    [self addDataWithTitle:@"[我页面引导][清空][宝宝记 110]" selector:@selector(da_jindouCover_clearBBJ:)];
    [self addDataWithTitle:@"[我页面引导][清空][金豆 108]" selector:@selector(da_jindouCover_clearJindou:)];
    
    [self addDataWithTitle:@"[ppp2][调试 manager][获取 plan]" selector:@selector(da_ppp2_debugManagerGetPlan:)];
    [self addDataWithTitle:@"[ppp2][调试 manager][曝光 plan]" selector:@selector(da_ppp2_debugManagerExpPlan:)];
    [self addDataWithTitle:@"[ppp2][调试 manager][关闭 plan]" selector:@selector(da_ppp2_debugManagerClosePlan:)];
    [self addDataWithTitle:@"[ppp2][调试 manager][reset]" selector:@selector(da_ppp2_debugManagerReset:)];
    
    [self addDataWithTitle:@"[popplan-mytab][查看][日志]" selector:@selector(da_mytabPopplan_looklog:)];
    [self addDataWithTitle:@"[popplan-mytab][重置][日志]" selector:@selector(da_mytabPopplan_resetLog:)];
    [self addDataWithTitle:@"[popplan-mytab][重置]" selector:@selector(da_mytabPopplan_reset:)];
}

- (void)setupUI {
    [self addSubview:self.dismissButton];
    [self addSubview:self.miniButton];
    [self addSubview:self.cButton];
    [self addSubview:self.tableView];
    
    [self.dismissButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(0);
        make.leading.equalTo(self).offset(0);
        make.width.equalTo(@50);
        make.height.mas_equalTo(@50);
    }];

    [self.miniButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(0);
        make.leading.equalTo(self.dismissButton.mas_trailing).offset(0);
        make.width.equalTo(@50);
        make.height.mas_equalTo(@50);
    }];
    
    [self.cButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(0);
        make.leading.equalTo(self.miniButton.mas_trailing).offset(0);
        make.width.equalTo(@50);
        make.height.mas_equalTo(@50);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top).offset(50);
        make.leading.trailing.equalTo(self);
        make.bottom.equalTo(self).offset(0);
    }];
}

// MARK: - Public

+ (void)show {
    NSInteger tag = 123666777;
    UIWindow *window = UIApplication.sharedApplication.delegate.window;
    IMYSurveyDebugView *debugView = nil;
    debugView = [window imy_findViewWithTag:tag];
    if (debugView) {
        [debugView dismiss];
    }
    
    debugView = [[IMYSurveyDebugView alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT, SCREEN_WIDTH, 50.0 * 4.0)];
    debugView.tag = tag;
    [UIApplication.sharedApplication.delegate.window addSubview:debugView];
}

- (void)dismiss {
    [self removeFromSuperview];
}

// MARK: - Data action

- (void)addDataWithTitle:(NSString *)title selector:(SEL)selector {
    NSDictionary *dict = @{
        @"title": title,
        @"selector": NSStringFromSelector(selector)
    };
    [self.datas addObject:dict];
}

- (void)da_home2ndfloor_debugRefreshHeader:(NSString *)title {
    NSString *key = @"#+DEBUG2NDFLOORREFRESHHEADER";
    BOOL isDebug = [[IMYKV defaultKV] boolForKey:@"#+DEBUG2NDFLOORREFRESHHEADER"];
    isDebug = !isDebug;
    [[IMYKV defaultKV] setBool:isDebug forKey:key];
    [UIWindow imy_showTextHUD:@"开启二楼刷新控件调试：%@", @(isDebug)];
}

- (void)da_home2ndfloor_guide:(NSString *)title {
    NSString *key = @"#+imy-home2ndfloor-guide-forceShow";
    BOOL isDebug = [[IMYKV defaultKV] boolForKey:key];
    isDebug = !isDebug;
    [[IMYKV defaultKV] setBool:isDebug forKey:key];
    [UIWindow imy_showTextHUD:@"开启二楼引导动画调试：%@", @(isDebug)];
}

- (void)da_home2ndfloor_cleanGuideLastShowTime:(NSString *)title {
    NSString *key = [NSString stringWithFormat:@"imy-home2ndfloor-guide-lastShowTime-%@", IMYPublicAppHelper.shareAppHelper.userid];
    [[IMYKV defaultKV] removeForKey:key];
}

- (void)da_home2ndfloor_log:(NSString *)title {
    NSArray *key = @"#+imykv-2ndfloor-log";
    NSArray *logs = [[IMYKV defaultKV] arrayForKey:key];
    if (logs == nil) {
        logs = @[];
    }
    
    NSString *logString = [logs componentsJoinedByString:@"\n"];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:logString];
    
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_home2ndfloor_log_clean:(NSString *)title {
    NSArray *key = @"#+imykv-2ndfloor-log";
    [[IMYKV defaultKV] removeForKey:key];
}

- (void)da_jindouCover_clearHomePage:(NSString *)title {
    NSString *homePageShowKey = [NSString stringWithFormat:@"IMYMeMine2CoverViewShowed-homePage-%@", [IMYPublicAppHelper shareAppHelper].userid];
    [[IMYKV defaultKV] removeForKey:homePageShowKey];
    [UIWindow imy_showTextHUD:@"succeed"];
}

- (void)da_jindouCover_clearBBJ:(NSString *)title {
    NSString *babyShowedKey = [NSString stringWithFormat:@"IMYMeMine2CoverViewShowed-BabyGuid-%@", [IMYPublicAppHelper shareAppHelper].userid];
    [[IMYKV defaultKV] removeForKey:babyShowedKey];
    [UIWindow imy_showTextHUD:@"succeed"];
}

- (void)da_jindouCover_clearJindou:(NSString *)title {
    NSString *goldbeanShowedKey = [NSString stringWithFormat:@"IMYMeMine2CoverViewShowed-%@", [IMYPublicAppHelper shareAppHelper].userid];
    [[IMYKV defaultKV] removeForKey:goldbeanShowedKey];
    [UIWindow imy_showTextHUD:@"succeed"];
}

- (void)da_ppp2_lookLog:(NSString *)title {
    NSArray *logs = [IMYPopplanV2Manager debugGetLogs];
    NSString *logString = [logs componentsJoinedByString:@"\n"];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:logString];
    
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_ppp2_lookExpinfo:(NSString *)title {
    NSArray *expInfos = [IMYPopplanV2Manager debugGetExpinfos];
    NSString *logString = [expInfos componentsJoinedByString:@"\n"];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:logString];
    
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_ppp2_lookPlanList:(NSString *)title {
    NSArray *plansList = [IMYPopplanV2Manager debugGetPlansList];
    NSString *logString = [plansList componentsJoinedByString:@"\n"];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:logString];
    
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_ppp2_lookLaunchCache:(NSString *)title {
    NSString *dirPath = [[NSString imy_documentsDirectory] stringByAppendingPathComponent:@"launch_img_v2"];
    NSArray *fileNames = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:dirPath error:nil];
    
    NSString *ret = [fileNames componentsJoinedByString:@"\n"];
    NSString *introduce = [[IMYKV defaultKV] stringForKey:@"kIMYUserIntroduceKey"];
    NSInteger introducePlanId = [[IMYKV defaultKV] integerForKey:@"kIMYUserIntroducePlanIdKey"];
    ret = [ret stringByAppendingFormat:@"\nintroduce = %@\nplanId = %ld", introduce, introducePlanId];
    
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:ret];
    
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_ppp2_lookLaunchCache_index:(NSString *)title {
    NSString *key = @"IMYPopplanV2MaterialModel-item4index";
    NSInteger index = [[IMYKV defaultKV] integerForKey:key];
    [UIWindow imy_showTextHUD:[NSString stringWithFormat:@"开屏轮播索引 %ld", index]];
}

- (void)da_ppp2_resetPopplanManager:(NSString *)title {
    [IMYPopplanV2Manager debugReset];
}

- (void)da_ppp2_resetLaunchCache:(NSString *)title {
    // 1、删文件
    NSString *dirPath = [[NSString imy_documentsDirectory] stringByAppendingPathComponent:@"launch_img_v2"];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    [fileManager removeItemAtPath:dirPath error:nil];
    
    // 2、删 key
    [[IMYKV defaultKV] removeForKey:@"kIMYUserIntroduceKey"];
    [[IMYKV defaultKV] removeForKey:@"kIMYUserIntroducePlanIdKey"];
}

- (void)da_ppp2_resetItem4Index:(NSString *)title {
    [[IMYKV defaultKV] removeForKey:@"IMYPopplanV2MaterialModel-item4index"];
}

- (void)da_ppp2_debugManagerGetPlan:(NSString *)title {
    self.planModel = [[IMYPopplanV2Manager sharedInstance] getPlanWithCode:@"mytab"];
}

- (void)da_ppp2_debugManagerExpPlan:(NSString *)title {
    if (self.planModel) {
        NSString *time = [[NSDate date] imy_getDateTimeString];
        [[IMYPopplanV2Manager sharedInstance] updateExpinfoWithCode:@"mytab" planId:self.planModel.id date:[NSDate date]];
    }
    NSLog(@"[ppp2][曝光][计划 = %ld]", self.planModel.id);
}

- (void)da_ppp2_debugManagerClosePlan:(NSString *)title {
    if (self.planModel) {
        [[IMYPopplanV2Manager sharedInstance] updateExpinfoCloseWithCode:@"mytab" planId:self.planModel.id close:YES];
    }
    NSLog(@"[ppp2][关闭][计划 = %@]", self.planModel.name);
}

- (void)da_ppp2_debugManagerReset:(NSString *)title {
    [IMYPopplanV2Manager debugReset];
}

- (void)da_survey_lookLog:(NSString *)title {
    NSString *kvkey = @"#+IMYPopPlanManager-logArray";
    NSArray *logs = [[IMYKV defaultKV] arrayForKey:kvkey];
    NSString *logString = [logs componentsJoinedByString:@"\n"];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:logString];
    
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_survey_lookShowInfo:(NSString *)title {
    NSArray *showInfos = [[IMYPopPlanManager sharedInstance] debugGetAllShowInfos];
    NSString *logString = [showInfos componentsJoinedByString:@"\n"];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:logString];
    
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_survey_lookPlanList:(NSString *)title {
    NSArray *list = [[IMYPopPlanManager sharedInstance] debugGetAllPlanList];
    NSString *content = [list componentsJoinedByString:@"\n"];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:content];
    
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_survey_lookGfc:(NSString *)title {
    NSDictionary *gfc = [[IMYPopPlanManager sharedInstance] debugGetAllGfcPolicy];
    NSString *content = [gfc imy_jsonString];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:content];
    
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_survey_lookSurveyList:(NSString *)title {
    NSArray *list = [[IMYSurveySDK sharedInstance] debugGetAllServey];
    NSString *content = [list componentsJoinedByString:@"\n"];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:content];
    
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_survey_lookLastShowTime:(NSString *)title {
    NSString *content = [[IMYPopPlanManager sharedInstance] debugGetLastShowTime];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:content];
    
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_survey_resetLog:(NSString *)title {
    NSString *kvkey = @"#+IMYPopPlanManager-logArray";
    [[IMYKV defaultKV] removeForKey:kvkey];
    
    imy_asyncMainBlock(^{
        [UIWindow imy_showTextHUD:@"succeed"];
    });
}

- (void)da_survey_reset:(NSString *)title {
    [[NSNotificationCenter defaultCenter] postNotificationName:@"NOTI_IMYSurveySDK_CLEAN" object:nil];
    imy_asyncMainBlock(^{
        [UIWindow imy_showTextHUD:@"succeed"];
    });
}

- (void)da_mytabPopplan_looklog:(NSString *)title {
    NSString *kvkey = @"#+debugPopupPlanViewLog";
    NSArray *logs = [[IMYKV defaultKV] arrayForKey:kvkey];
    NSString *logString = [logs componentsJoinedByString:@"\n"];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 16, 350)];
    [textView setText:logString];
    [IMYActionMessageBox showBoxWithTitle:title
                                     view:textView
                                    style:IMYMessageBoxStyleFlatten
                               showInView:nil
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
    }];
}

- (void)da_mytabPopplan_resetLog:(NSString *)title {
    [IMYPopupPlanView addLog:nil];
    [UIWindow imy_showTextHUD:@"好了"];
}

- (void)da_mytabPopplan_reset:(NSString *)title {
    [IMYPopupPlanView clearPopupPlanData];
    [UIWindow imy_showTextHUD:@"好了"];
}

- (void)da_bizChat_openBizChat:(NSString *)title {
    [[IMYURIManager sharedInstance] runActionWithString:@"chat/biz"];
}

// MARK: - Button Action

- (void)handleDismissButtonAction:(UIButton *)sender {
    [self dismiss];
}

- (void)handleMiniButtonAction:(id)sender {
    if (!self.tableView.hidden) {
        // 最小化
        CGFloat width = 50 * 2;
        CGFloat height = 50;
        self.frame = CGRectMake(SCREEN_WIDTH - width, (SCREEN_HEIGHT / 2.0) - height, width, height);
        self.tableView.hidden = YES;
    } else {
        // 最大化
        CGFloat width = SCREEN_WIDTH;
        CGFloat height = 50 * 4;
        self.frame = CGRectMake(0, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT, width, height);
        self.tableView.hidden = NO;
    }
}

- (void)handleCButtonAction:(UIButton *)sender {
    [self setupDatas];
    [self.tableView reloadData];
}

// MARK: - Action

- (void)handlePan:(UIPanGestureRecognizer *)pan {
    CGPoint point = [pan translationInView:self];
    self.center = CGPointMake(self.center.x + point.x, self.center.y + point.y);
    [pan setTranslation:CGPointZero inView:self];

    if (pan.state == UIGestureRecognizerStateEnded && self.tableView.hidden == NO) {
        CGSize screenSize = [UIScreen mainScreen].bounds.size;
        self.center = CGPointMake(screenSize.width / 2, self.center.y);
    }
}

// MARK: - UITableViewDelegate

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.datas.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 50;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"IMYSurveyDebugViewCell"];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"IMYSurveyDebugViewCell"];
        cell.backgroundColor = [UIColor clearColor];
        cell.contentView.backgroundColor = [UIColor clearColor];
        cell.textLabel.textColor = [UIColor whiteColor];
    }

    NSDictionary *data = self.datas[indexPath.row];
    cell.textLabel.text = data[@"title"];
    cell.textLabel.adjustsFontSizeToFitWidth = YES;

    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];

    NSDictionary *data = self.datas[indexPath.row];
    void (^action)(void) = data[@"action"];
    if (action) {
        action();
    }
    
    NSString *selectorString = data[@"selector"];
    NSString *title = data[@"title"];
    SEL selector = NSSelectorFromString(selectorString);
    if (selector && [self canPerformAction:selector withSender:nil]) {
        [self performSelector:selector withObject:title];
    }
}

// MARK: - Get

- (UIButton *)dismissButton {
    if (!_dismissButton) {
        _dismissButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_dismissButton setTitle:@"X" forState:UIControlStateNormal];
        [_dismissButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_dismissButton addTarget:self action:@selector(handleDismissButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _dismissButton;
}

- (UIButton *)miniButton {
    if (!_miniButton) {
        _miniButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_miniButton setTitle:@"M" forState:UIControlStateNormal];
        [_miniButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_miniButton addTarget:self action:@selector(handleMiniButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _miniButton;
}

- (UIButton *)cButton {
    if (!_cButton) {
        _cButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cButton setTitle:@"C" forState:UIControlStateNormal];
        [_cButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_cButton addTarget:self action:@selector(handleCButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cButton;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] init];
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.delegate = self;
        _tableView.dataSource = self;
    }
    return _tableView;
}

@end

#endif
