//
//  IMYMeUserInfoVC.m
//  IMYMe
//
//  Created by ljh on 2023/6/6.
//

#import "IMYMeUserInfoVC.h"
#import "IMYMeUserInfoCell.h"
#import "SYUserHelper.h"
#import <IMYBaseKit/IMYViewKit.h>
#import "IMYCityPickVC.h"
#import "SYPublicFun.h"
#import "SYEditNicknameVC.h"
#import "SYSingleSelectViewController.h"
#import <IMYAccount/IMYAccountUserInfoService.h>
#import "IMYMeLoginManager.h"
#import "IMYMeMineABManager.h"
#if __has_include(<IMYRecord/IMYRecord.h>)
#import <IMYRecord/IMYRecord.h>
#endif
#if __has_include(<ZZIMYMain/SYHospitalVC.h>)
#import <ZZIMYMain/SYHospitalVC.h>
#endif

@interface IMYMeUserInfoVC () <UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray<NSArray *> *sections;
@end

@implementation IMYMeUserInfoVC

- (void)_initMyself {
    [super _initMyself];
    self.sourceType = 1;
    
    // 防止头像无法刷新
    [SYPublicFun setupIsForbidAvatarRefresh:NO isNotify:NO];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"个人信息";
    [self setupData];
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    @weakify(self);
    [[[IMYPublicAppHelper shareAppHelper].useridChangedSignal skip:1] subscribeNext:^(id x) {
        @strongify(self);
        [self fetchUserInfo];
    }];
    
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"K_FetchAvatarFromMeSucceed" object:nil] deliverOnMainThread] subscribeNext:^(NSNotification *notification) {
        @strongify(self);
        [self.tableView reloadData];
    }];
    
    //伴侣信息 刷新
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"kNotificationRelativeUserInfoUpdateNotification" object:nil] deliverOnMainThread] subscribeNext:^(NSNotification *notification) {
        @strongify(self);
        [self setupData];
        [self.tableView reloadData];
    }];
    
    // 进入页面需要重新请求me接口
    [self fetchUserInfo];
}

- (void)viewDidAppear:(BOOL)animated {
    if (!self.isViewDidAppeared) {
        // 生日曝光埋点
        [IMYGAEventHelper postWithPath:@"event"
                                params:@{
            @"event":@"wd_gdzl_sr",
            @"action":@(1),
            @"public_info":@(self.sourceType),
        } headers:nil completed:nil];
    }
    [super viewDidAppear:animated];
}

- (void)setupData {
    NSMutableArray *sections = [NSMutableArray array];
    
    {
        NSMutableArray *datas = [NSMutableArray array];
        [sections addObject:datas];
        
        {
            IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
            model.type = IMYMeUserInfoDataTypeAvatar;
            model.title = @"头像";
            
            NSString *avatarURL = nil;
            IMYABTestVariables *vars = [[IMYCommonConfig sharedInstance] configForKey:@"disable_operation"];
            // 头像, 优先使用服务端下发的配置头像URL
            if ([vars boolForKey:@"default_avatar"]) {
                avatarURL = [vars stringForKey:@"default_avatar_pic"];
            } else if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
                //头像接口下发为空"avatar": "",则显示默认的头像
                avatarURL = imy_isBlankString([SYUserHelper sharedHelper].headImageFileName) ? nil : [SYUserHelper sharedHelper].headImageURL;
            } else {
                avatarURL = [SYUserHelper sharedHelper].headImageURL;
            }
            model.avatar = avatarURL;
            // [审核中]
            if ([SYUserHelper sharedHelper].avatar_state == 1) {
                model.tip = @"审核中";
            }
            [self addModel:model toDatas:datas];
        }
        
        {
            IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
            model.type = IMYMeUserInfoDataTypeNickname;
            model.title = @"昵称";
            // [副标题]
            IMYABTestVariables *vars = [[IMYCommonConfig sharedInstance] configForKey:@"disable_operation"];
            // 昵称, 优先使用服务端下发的配置文案
            if ([vars boolForKey:@"default_nickname"]) {
                // 使用服务端下发的配置文案
                model.detail = [vars stringForKey:@"default_nickname_txt"];
            } else if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
                NSString *nickname = [SYUserHelper sharedHelper].screen_name;
                if (imy_isEmptyString(nickname)) {
                    nickname = @"请先设置你的昵称";
                }
                model.detail = nickname;
            } else {
                model.detail = @"未登录";
            }
            // [审核中]
            if ([SYUserHelper sharedHelper].screen_name_state == 1) {
                model.tip = @"审核中";
            }
            [self addModel:model toDatas:datas];
        }
    }
    
    {
        NSMutableArray *datas = [NSMutableArray array];
        
        {
            IMYGroupRelationModel *relationModel = [[IMYPublicAppHelper shareAppHelper] userRelationModel];
            if (relationModel) {
                IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
                model.type = IMYMeUserInfoDataTypePartner;
                model.title = @"伴侣";
                NSString *screen_name = relationModel.personInfo.screen_name;
                NSString *avatar = relationModel.personInfo.avatar;
                model.detail = imy_isNotEmptyString(screen_name) ? screen_name : @"我的伴侣";
                model.avatar = avatar;
                [self addModel:model toDatas:datas];
            }
        }
        
        if (datas.count > 0) {
            [sections addObject:datas];
        }
    }
    
    {
        if ([IMYMeMineABManager shouldShowPendantEntrance]) {
            NSMutableArray *datas = [NSMutableArray array];
            
            {
                IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
                model.type = IMYMeUserInfoDataTypeAvatarGuajian;
                model.title = IMYString(@"头像挂件");
                
//                // TODO: 【头像挂件】上新
//                BOOL isNewGuajian = YES;
//                if (isNewGuajian) {
//                    model.detail = IMYString(@"有上新");
//                }
                [self addModel:model toDatas:datas];
            }
            
            if (datas.count > 0) {
                [sections addObject:datas];
            }

        }
    }
    
    {
        NSMutableArray *datas = [NSMutableArray array];
        [sections addObject:datas];
        
        {
            IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
            model.type = IMYMeUserInfoDataTypeLevel;
            model.title = @"等级";
            if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
                model.level = MAX(0, [SYUserHelper sharedHelper].userRank);
                model.detail = [NSString stringWithFormat:@"LV%ld", model.level];
            } else {
                model.detail = @"未登录";
            }
            [self addModel:model toDatas:datas];
        }
    }
    
    {
        NSMutableArray *datas = [NSMutableArray array];
        [sections addObject:datas];
        
        {
            IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
            model.type = IMYMeUserInfoDataTypeBirthday;
            model.title = @"生日";
            
            NSString *birthday = [IMYPublicAppHelper shareAppHelper].birthday;
            if (!birthday.length) {
                model.detail = @"请填写生日";
            } else {
                // 兼容 yyyy-MM-dd / yyyy-M-d
                NSDate *birthdayDate = [birthday imy_getOnlyDate] ?: [[NSDateFormatter imy_getShortDateFormater] dateFromString:birthday];
                model.detail = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:birthdayDate] ?: birthday;
            }
            [self addModel:model toDatas:datas];
        }
        
        {
            IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
            model.type = IMYMeUserInfoDataTypeHeight;
            model.title = @"身高";
            const CGFloat userHeight = [SYUserHelper sharedHelper].heigth;
            if (userHeight < 1) {
                model.detail = @"请填写身高";
            } else {
                model.detail = [NSString stringWithFormat:@"%.1lf厘米", userHeight];
            }
            [self addModel:model toDatas:datas];
        }
        
        {
            IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
            model.type = IMYMeUserInfoDataTypeCity;
            model.title = @"城市";
            NSString *city = [SYUserHelper sharedHelper].city;
            if (imy_isBlankString(city)) {
                model.detail = @"请选择城市";
            } else {
                model.detail = city;
            }
            [self addModel:model toDatas:datas];
        }
        
        {
            IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
            model.type = IMYMeUserInfoDataTypeIPLocation;
            model.title = @"IP属地";
            model.detail = [SYUserHelper sharedHelper].ipLocation;
            [self addModel:model toDatas:datas];
        }
        
        // 怀孕身份下，才显示妇产医院
        if (IMYPublicAppHelper.shareAppHelper.userMode == IMYVKUserModePregnancy) {
            IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
            model.type = IMYMeUserInfoDataTypeHospital;
            model.title = @"妇产医院";
            NSString *hospital = [SYUserHelper sharedHelper].hospital;
            if (imy_isBlankString(hospital)) {
                model.detail = @"请选择妇产医院";
            } else {
                model.detail = hospital;
            }
            [self addModel:model toDatas:datas];
        }
    }
    
    {
        NSMutableArray *datas = [NSMutableArray array];
        [sections addObject:datas];
        
        {
            IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
            model.type = IMYMeUserInfoDataTypeAddress;
            model.title = @"收货地址";
            [self addModel:model toDatas:datas];
        }
        
        {
            BOOL isShow = [[IMYConfigsCenter sharedInstance] boolForKeyPath:@"meetyou_app_setting.account_number.is_show"];
            if (isShow) {
                IMYMeUserInfoDataModel *model = [IMYMeUserInfoDataModel new];
                model.type = IMYMeUserInfoDataTypeBizAccount;
                model.title = @"收款账号";
                [self addModel:model toDatas:datas];
            }
        }
    }
    
    self.sections = sections;
}

- (void)addModel:(IMYMeUserInfoDataModel *)model toDatas:(NSMutableArray *)datas {
    BOOL isQinyou = [IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeQinYou;
    
#ifdef DEBUG
    NSNumber *forceQinYouMode = [[NSUserDefaults standardUserDefaults] objectForKey:@"#+ForceQinYouMode"];
    if (forceQinYouMode.boolValue) {
        isQinyou = YES;
    }
#endif
    
    // 亲友模式只展示 头像 和 昵称
    if (isQinyou) {
        if (model.type == IMYMeUserInfoDataTypeAvatar || model.type == IMYMeUserInfoDataTypeNickname || model.type == IMYMeUserInfoDataTypePartner) {
            [datas addObject:model];
        }
        return;
    }
    
    [datas addObject:model];
}

#pragma mark - UITableView

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.sections.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSArray *datas = self.sections[section];
    return datas.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYMeUserInfoCell *cell = [tableView dequeueReusableCellWithIdentifier:@"cell" forIndexPath:indexPath];
    
    NSArray *datas = self.sections[indexPath.section];
    
    IMYMeUserInfoDataModel *model = datas[indexPath.row];
    cell.model = model;
    
    cell.lineView.hidden = YES;
    if (datas.count > 1) {
        if (indexPath.row == 0) {
            [cell.edgeBoxView imy_drawTopCornerRadius:12];
            cell.lineView.hidden = NO;
        } else if (indexPath.row == datas.count - 1) {
            [cell.edgeBoxView imy_drawBottomCornerRadius:12];
        } else {
            [cell.edgeBoxView imy_drawBottomCornerRadius:0];
            cell.lineView.hidden = NO;
        }
    } else {
        [cell.edgeBoxView imy_drawAllCornerRadius:12];
    }
    
    @weakify(self);
    cell.imyut_eventInfo.eventName = @"IMYMeUserInfoVC-banlvCell";
    [cell.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        NSMutableDictionary *dict = [[NSMutableDictionary alloc] init];
        [dict imy_setNonNilObject:@(1) forKey:@"action"];
        [dict imy_setNonNilObject:@"yy_grxxy_blrk" forKey:@"event"];
        [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
    }];

    
    return cell;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 8;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *datas = self.sections[indexPath.section];
    IMYMeUserInfoDataModel *model = datas[indexPath.row];
    
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        // 未登录情况，下列类型 需要先登录
        switch (model.type) {
            case IMYMeUserInfoDataTypeAvatar:
            case IMYMeUserInfoDataTypeNickname:
            case IMYMeUserInfoDataTypeLevel:
            case IMYMeUserInfoDataTypeAddress:
            case IMYMeUserInfoDataTypeBizAccount: {
                [[IMYURIManager shareURIManager] runActionWithString:@"login"];
                return;
            } break;
            default:
                break;
        }
    }
    
    switch (model.type) {
        case IMYMeUserInfoDataTypeAvatar: {
            [IMYEventHelper event:@"wdzl-tx"];
            if (SYUserHelper.sharedHelper.avatar_state == 1) {
                [UIWindow imy_showTextHUD:IMYString(@"您的头像正在审核中，暂不支持修改")];
                return;
            }
            IMYABTestVariables *vars = [IMYCommonConfig.sharedInstance configForKey:@"disable_operation"];
            if ([vars boolForKey:@"avatar_edit"]) {
                NSString *toast = [vars stringForKey:@"avatar_edit_toast"];
                [UIWindow imy_showTextHUD:toast];
                return;
            }
            [self verifyUserState];
        } break;
        case IMYMeUserInfoDataTypeNickname: {
            [IMYEventHelper event:@"wdzl-nc"];
            if (SYUserHelper.sharedHelper.screen_name_state == 1) {
                [UIWindow imy_showTextHUD:IMYString(@"您的昵称正在审核中，暂不支持修改")];
                return;
            }
            IMYABTestVariables *vars = [IMYCommonConfig.sharedInstance configForKey:@"disable_operation"];
            if ([vars boolForKey:@"nickname_edit"]) {
                NSString *toast = [vars stringForKey:@"nickname_edit_toast"];
                [UIWindow imy_showTextHUD:toast];
                return;
            }
            @weakify(self);
            SYEditNicknameVC *evc = SYEditNicknameVC.new;
            evc.editFinishBlock = ^{
                @strongify(self);
                [self fetchUserInfo];
            };
            [self imy_push:evc];
        } break;
        case IMYMeUserInfoDataTypeLevel: {
            [IMYEventHelper event:@"gdzl-wddj"];
            IMYVKWebViewController *vc = [IMYVKWebViewController webWithURLString:WebURL我的等级];
            [self imy_push:vc];
        } break;
        case IMYMeUserInfoDataTypeBirthday: {
            [IMYEventHelper event:@"gdzl-sr"];
            //生日
            NSDate *minDate = [[NSDateFormatter imy_getDateFormater] dateFromString:@"1900-01-01"];
            NSDate *maxDate = [NSDate imy_today];
            NSDate *defaultDate = nil;
            NSDate *birthdayDate = nil;
            NSString *birthday = [IMYPublicAppHelper shareAppHelper].birthday;
            if (!birthday.length) {
                // 统一默认日期
                defaultDate = [IMYPublicAppHelper defaultBirthdayForPicker];
                birthdayDate = defaultDate;
            } else {
                // 兼容 yyyy-MM-dd / yyyy-M-d
                birthdayDate = [birthday imy_getOnlyDate] ?: [[NSDateFormatter imy_getShortDateFormater] dateFromString:birthday];
            }
            @weakify(self);
            IMYPickerView *pickerView = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES dataArray:@[minDate, maxDate] pickerViewTpe:IMYPickerViewTypeDate confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                @strongify(self);
                NSDateFormatter *format = [NSDateFormatter imy_getCN_DateFormater2];
                model.detail = [format stringFromDate:result.resultDate];
                
                [SYUserHelper sharedHelper].birthday = result.resultDate.imy_getOnlyDateString;
                [[SYUserHelper sharedHelper] saveToDB];
                [SYPublicFun uploadUserInfoData];
                
                [self.tableView reloadData];
                
                // https://www.tapd.cn/21039721/prong/stories/view/1121039721001117914
                NSInteger publicInfo = self.sourceType;
                NSInteger publicType = 2;
                if (defaultDate && [result.resultDate isEqualToDate:defaultDate]) {
                    publicType = 1;
                }
                [IMYGAEventHelper postWithPath:@"event"
                                        params:@{
                    @"event":@"wd_gdzl_sr",
                    @"action":@(2),
                    @"public_info":@(publicInfo),
                    @"public_type":@(publicType),
                } headers:nil completed:nil];
            } cancelBlock:^{
                @strongify(self);
            }];
            pickerView.title = IMYString(@"生日");
            [pickerView setSelectWithDate:birthdayDate];
            [pickerView show];
        } break;
        case IMYMeUserInfoDataTypeHeight: {
            [IMYEventHelper event:@"gdzl-sg"];
            //身高
            const NSInteger minRow = 60;
            const NSInteger maxRow = 250;
            NSMutableArray *oneArray = [NSMutableArray arrayWithCapacity:(maxRow - minRow + 1)];
            NSMutableArray *twoArray = [NSMutableArray arrayWithCapacity:10];
            for (int i = minRow; i <= maxRow; i++) {
                [oneArray addObject:[NSString stringWithFormat:@"%d", i]];
            }
            for (int i = 0; i < 10; i++) {
                [twoArray addObject:[NSString stringWithFormat:@".%d", i]];
            }
            NSInteger oneSelected = 0;
            NSInteger twoSelected = 0;
            NSInteger intHeight = [SYUserHelper sharedHelper].heigth * 10;
            if (intHeight < 10) {
                //无身高记录的用户，默认定位到160cm
                oneSelected = 160 - minRow;
            } else {
                oneSelected = intHeight / 10 - minRow;
                twoSelected = intHeight % 10;
            }
            @weakify(self);
            IMYPickerView *pickerView = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES dataArray:@[oneArray, twoArray] pickerViewTpe:IMYPickerViewTypeCustom confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                @strongify(self);
                NSInteger row0 = [resultArray[0] integerValue] + minRow;
                NSInteger row1 = [resultArray[1] integerValue];
                
                CGFloat userHeight = [NSString stringWithFormat:@"%ld.%ld", row0, row1].doubleValue;
                model.detail = [NSString stringWithFormat:@"%.1lf厘米", userHeight];
                
                [SYUserHelper sharedHelper].heigth = userHeight;
                [[SYUserHelper sharedHelper] saveToDB];
                [SYPublicFun uploadUserInfoData];
                
                [self.tableView reloadData];
            } cancelBlock:^{
                
            }];
            [pickerView setUnions:@[@"厘米"] forComponent:1 position:IMYPickerViewUnionPositionRight longestTitle:@"11"];
            pickerView.title = IMYString(@"身高");
            [pickerView setSelectWithArray:@[@(oneSelected), @(twoSelected)]];
            [pickerView show];
        } break;
        case IMYMeUserInfoDataTypeMarry: {
            [IMYEventHelper event:@"gdzl-hyzk"];
            NSArray *array = @[IMYString(@"未婚"), IMYString(@"已婚")];
            NSUInteger index = [SYUserHelper sharedHelper].bMarry ? 1 : 0;
            SYSingleSelectViewController *vc = [[SYSingleSelectViewController alloc] initWithItems:array atIndex:index];
            vc.navigationItem.title = IMYString(@"婚姻状况");
            [self imy_push:vc];
            @weakify(self);
            [vc setSelectedBlock:^(const NSUInteger blockIndex) {
                @strongify(self);
                const BOOL isMarry = (blockIndex != 0);
                if ([SYUserHelper sharedHelper].bMarry != isMarry) {
                    model.detail = array[blockIndex];
                    [SYUserHelper sharedHelper].bMarry = (blockIndex != 0);
                    [[SYUserHelper sharedHelper] saveToDB];
                    [SYPublicFun uploadUserInfoData];
                    [self.tableView reloadData];
                }
            }];
        } break;
        case IMYMeUserInfoDataTypeCity: {
            [IMYEventHelper event:@"gdzl-cs"];
            IMYCityPickVC *vc = [[IMYCityPickVC alloc] init];
            @weakify(self);
            [vc setCompletion:^(NSString *cityOrCountry, NSInteger code) {
                @strongify(self);
                //更新数据
                model.detail = cityOrCountry;
                //更新界面
                [self.tableView reloadData];
                //保存数据
                [SYUserHelper sharedHelper].city = cityOrCountry;
                [[SYUserHelper sharedHelper] saveToDB];
                //同步数据
#if __has_include(<IMYRecord/IMYRecord.h>)
                [IMYPregnanceModel refreshOtherViews];
#endif
                [SYPublicFun uploadUserInfoData];
            }];
            [self imy_push:vc];
        } break;
        case IMYMeUserInfoDataTypeHospital: {
            [IMYEventHelper event:@"gdzl-djfcyy"];
#if __has_include(<ZZIMYMain/SYHospitalVC.h>)
            SYHospitalVC *vc = [[SYHospitalVC alloc] init];
            if (imy_isNotEmptyString([SYUserHelper sharedHelper].hospital)) {
                vc.typeVC = SYHospitalVCTypeSameCity;
                vc.cityid = [SYUserHelper sharedHelper].hospitalCityId;
            } else {
                vc.typeVC = SYHospitalVCTypeProvince;
            }
            @weakify(self);
            [vc setCallBack:^{
                @strongify(self);
                model.detail = [SYUserHelper sharedHelper].hospital;
                [self.tableView reloadData];
            }];
            [self imy_push:vc];
#endif
        } break;
        case IMYMeUserInfoDataTypeAddress: {
            [IMYEventHelper event:@"gdzl-shdz"];
            [[IMYURIManager shareURIManager] runActionWithPath:@"user/address" params:@{ @"callFromNative": @(YES) } info:nil];
        } break;
        case IMYMeUserInfoDataTypeAvatarGuajian: {
            // TODO: 【头像挂件】跳转
            IMYURI *uri = [IMYURI uriWithPath:@"web/pure" params:@{@"url":[IMYMeMineABManager pendantRedirectUrl]} info:nil];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
        } break;
        case IMYMeUserInfoDataTypePartner: {
            //我的伴侣
            [[IMYURIManager shareURIManager] runActionWithString:@"mate/home"];
            
            NSMutableDictionary *dict = [[NSMutableDictionary alloc] init];
            [dict imy_setNonNilObject:@(2) forKey:@"action"];
            [dict imy_setNonNilObject:@"yy_grxxy_blrk" forKey:@"event"];
            [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
        } break;
        case IMYMeUserInfoDataTypeIPLocation: {
            // 无点击事件
        } break;
        case IMYMeUserInfoDataTypeBizAccount: {
            // 收款账号
            NSString *host = @"https://h5.youzibuy.com";
            
            NSString *envPath = @"/env_test";
            IMYURLEnviromentType envType = [IMYURLEnvironmentManager currentType];
            if (envType == IMYURLEnviromentTypeProduction) {
                envPath = @"/env_prod";
            } else if (envType == IMYURLEnviromentTypeYufa) {
                envPath = @"/env_pre";
            }
            
            NSString *suffix = @"/ybmall/pages/assets/account.html?is_show_loading=true&is_show_loading_bar=false&bar_style=white&bar_fixed=1&bar_effect=scroll_fadein_translate&is_pull_to_refresh=false";
            
            NSString *url = [NSString stringWithFormat:@"%@%@%@", host, envPath, suffix];
            [[IMYURIManager shareURIManager] runActionWithPath:@"ebweb" params:@{@"url": url} info:nil];
            
        } break;
        default: {
            NSAssert(NO, @"未实现的点击类型!");
        } break;
    }
    
}

#pragma mark - UI

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [UITableView new];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.estimatedRowHeight = UITableViewAutomaticDimension;
        _tableView.backgroundColor = [UIColor clearColor];
        [_tableView registerClass:IMYMeUserInfoCell.class forCellReuseIdentifier:@"cell"];
    }
    return _tableView;
}

#pragma mark - 头像处理

- (void)verifyUserState {
    @weakify(self);
    [UIWindow imy_showLoadingHUD];
    [[[IMYServerRequest getPath:@"/v2/status_code" host:users_seeyouyima_com params:@{@"type": @"avatar"} headers:nil] deliverOnMainThread]
     subscribeNext:^(IMYHTTPResponse *response) {
        @strongify(self);
        [UIWindow imy_hideHUD];
        NSDictionary * const dict = response.responseObject;
        if ([dict[@"status_code"] integerValue] == 1 || [dict[@"sensitive_forbid_avatar"] boolValue]) {
            [UIWindow imy_showTextHUD:dict[@"info"]];
        } else {
            [self touchMyHeadPicDo];
        }
    } error:^(NSError *error) {
        [UIWindow imy_hideHUD];
        if (![IMYNetState networkEnable]) {
            [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        } else {
            [UIWindow imy_showTextHUD:IMYString(@"操作失败，请重试哦~")];
            [IMYEventHelper event:@"ggtccx" attributes:@{@"来源": @"更换头像"}];
        }
    }];
}

- (void)touchMyHeadPicDo {
    @weakify(self);
    [self.view endEditing:YES];
    NSMutableArray *actions = [NSMutableArray arrayWithObjects:IMYString(@"从手机相册选择"), IMYString(@"拍照"), nil];
    if ([SYUserHelper sharedHelper].headImageURL.length || [SYUserHelper sharedHelper].headImageFileName.length) {
        [actions addObject:IMYString(@"查看大图")];
    }
    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                             otherTitles:actions
                                 summary:nil
                              showInView:self.navigationController.view
                                  action:^(NSInteger index) {
        @strongify(self);
        if (index == 0) {
            return;
        }
        if (index == 3) {
            [IMYEventHelper event:@"wdzl-ckdt"];
            IMYPhoto *photo = [IMYPhoto new];
            NSString *avatarURL = imy_isBlankString([SYUserHelper sharedHelper].headImageFileName) ? nil : [SYUserHelper sharedHelper].headImageURL;
            photo.sy_placeholder = [[SDImageCache sharedImageCache] imageFromDiskCacheForKey:avatarURL];
            photo.url = [NSURL URLWithString:avatarURL];
            [IMYPhotoBrowser showWithPhoto:photo];
            return;
        }
        if (index == 1) {
            [IMYEventHelper event:@"tjtp" label:IMYString(@"系统相册")];
        } else {
            [IMYEventHelper event:@"tjtp" label:IMYString(@"拍照")];
        }
        if (index == 2) {
            if ([UIImagePickerController needAlertForType:UIImagePickerControllerSourceTypeCamera]) {
                [UIAlertController imy_showAlertViewWithTitle:@""
                                                      message:[UIImagePickerController alertStringForType:UIImagePickerControllerSourceTypeCamera]
                                            cancelButtonTitle:IMYString(@"确定")
                                            otherButtonTitles:nil
                                                      handler:^(UIAlertController *alertController, NSInteger buttonIndex){
                    
                }];
                return;
            }
        }
        
        //7.8.8相册新需求
        if (index == 1) {
            //原有逻辑
            [IMYEventHelper event:@"xtxcdy" attributes:@{@"来源": @"头像"}];
            [[SYUserHelper sharedHelper] saveToDB];
            
            IMYAssetPickerController *vc = [[IMYAssetPickerController alloc] init];
            vc.styleType = IMYAssetPickerUITypeSingle;
            vc.delegate = self;
            vc.ratioSize = CGSizeMake(SCREEN_WIDTH, SCREEN_HEIGHT / 2);
            [self imy_present:vc animated:YES];
            
            return;
        }
        UIImagePickerController *imagePickerController = [[UIImagePickerController alloc] init];
        imagePickerController.allowsEditing = YES;
        imagePickerController.automaticallyAdjustsScrollViewInsets = NO;
        imagePickerController.extendedLayoutIncludesOpaqueBars = NO;
        imagePickerController.edgesForExtendedLayout = UIRectEdgeLeft | UIRectEdgeBottom | UIRectEdgeRight;
        imagePickerController.navigationController.automaticallyAdjustsScrollViewInsets = NO;
        imagePickerController.navigationController.extendedLayoutIncludesOpaqueBars = NO;
        imagePickerController.navigationController.edgesForExtendedLayout = UIRectEdgeLeft | UIRectEdgeBottom | UIRectEdgeRight;
        imagePickerController.delegate = (id)self;
        imagePickerController.modalPresentationStyle = UIModalPresentationOverCurrentContext;
        if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
            imagePickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
        } else {
            [UIWindow imy_showTextHUD:IMYString(@"您的设备没有摄像头!")];
            imagePickerController.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
        }
        [[SYUserHelper sharedHelper] saveToDB];
        [self presentViewController:imagePickerController
                           animated:YES
                         completion:^{
            [[UIApplication sharedApplication] setStatusBarStyle:UIStatusBarStyleDefault];
        }];
    }];
}

#pragma mark IMYAssetPickerControllerDelegate

- (void)assetPickerController:(IMYAssetPickerController *)assetPickerController didCropImage:(UIImage *)image {
    [self uploadImageToServer:image];
}

#pragma mark - ImagePicker Controller Delegate

- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingImage:(UIImage *)image editingInfo:(NSDictionary *)editingInfo NS_DEPRECATED_IOS(2_0, 3_0) {
    NSLog(@"didFinishPickingImage");
}


- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
    //修复10.3系统下闪退问题。
    if ([self.presentedViewController childViewControllers].count >= 1) {
        UICollectionView *collectionView = [[(UIViewController *)[[self.presentedViewController childViewControllers] objectAtIndex:0] view] imy_findSubviewWithClass:[UICollectionView class]];
        collectionView.delegate = nil;
        collectionView.dataSource = nil;
    }
    
    [self.presentedViewController dismissViewControllerAnimated:YES completion:nil];
    
    [[UIApplication sharedApplication] setStatusBarStyle:UIStatusBarStyleDefault];
    [[UIApplication sharedApplication] setStatusBarOrientation:UIInterfaceOrientationPortrait];
    [self setNeedsStatusBarAppearanceUpdate];
}

- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info {
    if (![IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:IMYString(@"请检查您的网络是否正常")];
        [self imagePickerControllerDidCancel:nil];
        return;
    }
    
    NSLog(@"didFinishPickingMediaWithInfo");
    NSString *mediaType = info[UIImagePickerControllerMediaType];
    //被选中的不是图片
    if (![mediaType isEqualToString:@"public.image"]) {
        [UIWindow imy_showTextHUD:IMYString(@"请选择图片")];
        return;
    }
    
    //获取照片实例
    UIImage *image = info[UIImagePickerControllerEditedImage];
    
    [self uploadImageToServer:image];
}

#pragma mark - 上传头像
- (void)uploadImageToServer:(UIImage *)image {
    CGSize imageSize = [image imy_pixelSize];
    if (imageSize.width < 320 || imageSize.height < 320) {
        [UIAlertController imy_quickAlert:IMYString(@"您上传的头像尺寸太小，请重新上传")];
        [self imagePickerControllerDidCancel:nil];
        return;
    }
    
    [UIWindow imy_showTextHUD:IMYString(@"正在上传头像，请稍等...")];
    
    NSData *imageData = UIImageJPEGRepresentation(image, 0.5);
    NSString *fileName = [NSString stringWithFormat:@"avatar_%@", [IMYPublicAppHelper shareAppHelper].userid];
    id<IMYOSSFileObject> fileObject = [[IMYOSS defaultUploader] fileObjectWithName:fileName data:imageData];
    fileObject.isAvatar = YES;
    fileObject.uploadScene = IMY_OSS_UPLOAD_SCENE_AVATAR;
    @weakify(self);
    [[IMYOSS defaultUploader] uploadObject:fileObject
                             progressBlock:nil
                            complatedBlock:^(id<IMYOSSFileObject> _Nonnull object, NSError *_Nonnull error) {
        [UIWindow imy_hideHUD];
        if (!error) {
            [IMYEventHelper event:@"txsccg"];
            NSString *avatarURL = [NSString stringWithFormat:@"%@/%@", sc_seeyouyima_com, object.name];
            [[SDImageCache sharedImageCache] storeImage:image recalculateFromImage:NO imageData:imageData forKey:avatarURL toDisk:YES];
            [SYUserHelper sharedHelper].headImageFileName = object.name;
            [[SYUserHelper sharedHelper] saveToDB];
            
            [[IMYAvatarManager sharedAvatarManager] removeAvatarEtagWithUserID:[IMYPublicAppHelper shareAppHelper].userid];
            [IMYAvatarManager sharedAvatarManager].userAvatarImage = image;
            
            [UIWindow imy_showTextHUD:IMYString(@"上传头像成功")];
            [self fetchUserInfo];
        } else {
            [IMYEventHelper event:@"txscsb"];
            NSString *errorMessage = nil;
            if ([error.domain isEqualToString:IMYMeetYouServiceErrorDomain]) {
                // 属于业务错误，获取服务端返回的错误文案
                errorMessage = error.userInfo[NSLocalizedDescriptionKey];
            }
            // 错误信息为空，则用默认文案
            if (imy_isBlankString(errorMessage)) {
                errorMessage = IMYString(@"上传头像失败，请重试");
            }
            [UIWindow imy_showTextHUD:errorMessage];
        }
        imy_asyncMainExecuteBlock(^{
            @strongify(self);
            [self imagePickerControllerDidCancel:nil];
            if (!error) {
                [[NSNotificationCenter defaultCenter] postNotificationName:@"SYUserInfoUploadAvatarSuccess" object:nil];
                [self.tableView reloadData];
            }
        });
    }];
}

#pragma mark - 获取用户信息

- (void)fetchUserInfo {
    [[IMYAccountUserInfoService getUserInfo] subscribeNext:^(id x) {
        NSDictionary *dict = x;
        if ([dict isKindOfClass:NSDictionary.class]) {
            IMYAccountUserModel *userModel = [[IMYAccountUserModel alloc] initWithRawDictionay:dict];
            if (![userModel.avatar isEqualToString:SYUserHelper.sharedHelper.headImageFileName]) {
                [[IMYAvatarManager sharedAvatarManager] removeAvatarEtagWithUserID:[IMYPublicAppHelper shareAppHelper].userid];
                [IMYAvatarManager sharedAvatarManager].userAvatarImage = nil;
            }
            [IMYMeLoginManager saveUserInfo:userModel skipMode:YES];
            // 刷新UI
            [self setupData];
            [self.tableView reloadData];
        }
    }];
}

@end
