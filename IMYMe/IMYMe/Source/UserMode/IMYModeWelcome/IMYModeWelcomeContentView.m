//
//  IMYModeWelcomeContentView.m
//  IMYMe
//
//  Created by lgw on 2022/8/5.
//

#import "IMYModeWelcomeContentView.h"
#import <IMYBaseKit/IMYViewKit.h>
#import "IMYCKLoadingTextButton.h"
#if __has_include(<IMYRecord/IMYRecordBabyManager.h>)
#import <IMYRecord/IMYRecordBabyManager.h>
#endif

@interface IMYModeWelcomeContentView ()
@property (nonatomic, strong) UIView *topView;
@property (nonatomic, strong) UILabel *titleLabel;
@property(nonatomic, strong) UIImageView *topBg2ImageView;
@property(nonatomic, strong) UIImageView *topBgImageView;
@property(nonatomic, strong) UIImageView *leftBgImageView;
@property(nonatomic, strong) UIImageView *rightBgImageView;

@property(nonatomic, strong) UILabel *title3Label;
@property(nonatomic, strong) UIImageView *centerImageView;

@end

@interface IMYModeWelcomeContentView (BiAnalytics)

- (void)closeYouthModeActionSheetExposuredAnalytics:(NSInteger)entrance;

- (void)closeYouthModeActionSheetClickAnalytics:(NSInteger)entrance
                                     publicType:(NSString *)publicType;

@end

@implementation IMYModeWelcomeContentView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self prepareUI];
        [self imy_drawTopCornerRadius:12];
//        self.viewModel = [IMYModeChangeContentModel_V2 defaultConfig];
//        self.titleLabel.text = self.viewModel.title;
    }
    return self;
}

#pragma mark - Action

- (void)close {
    !self.closeBlock ? : self.closeBlock();
}

#pragma mark - private method
- (void)prepareUI {
    CGFloat center_img_height = 284*(SCREEN_WIDTH/375.0);
    CGFloat const top_bg_img_height = SCREEN_WIDTH*(9.0/16.0);
    CGFloat center_img_bottom = 12; //中部切图距离下一步按钮间距默认12 如果放不下优先变小 0px   -- 优先级1
    CGFloat next_btn_bottom = 56 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN + SCREEN_STATUSBAR_HEIGHT; ////下一步按钮到底部默认56 如果小屏幕可以最小到12  -- 优先级2
    CGFloat title3_top = 30; //优先级1 2 还是放不下的时候 30 如果放不下优先变小 12px   -- 优先级3
    CGFloat title3_bottom = 4;
    CGFloat title_space = 4;

    
    CGFloat content_height = SCREEN_HEIGHT - SCREEN_STATUSBAR_HEIGHT;
    CGFloat dis_1 = content_height - (top_bg_img_height + top_bg_img_height + 37 + title_space + 24 + title_space + 55 + center_img_bottom + 48 + next_btn_bottom);
    if (dis_1 < 0) {
        center_img_bottom = 0;
        title_space = 2;
        dis_1 = content_height - (top_bg_img_height + top_bg_img_height + 37 + title_space + 24 + title_space + 55 + center_img_bottom + 48 + next_btn_bottom);
        if (dis_1 < 0 || ((iPhone5 || iPhone6p || iPhone6) && !iPhoneX)) {
            title3_bottom = 0;
            next_btn_bottom = 12 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN + SCREEN_STATUSBAR_HEIGHT;
//            dis_1 = content_height - (top_bg_img_height + top_bg_img_height + 37 + 4 + 24 + 4 + 55 + center_img_bottom + 48 + next_btn_bottom);
//            if (dis_1 < 0) {
//                title3_top = 12;
//            }
        }
    }
    
    CGFloat title3LabelT = 48;
    CGFloat nextBtnT = 47;
    /// 屏幕比为 9:16 的 归属小屏幕
    // 允许一定的浮动范围，以应对浮点数计算中的微小误差
    CGFloat epsilon = 0.01;
    CGSize screenSize = [UIScreen mainScreen].bounds.size;
    CGFloat screenRatio = screenSize.width / screenSize.height;
    CGFloat targetRatio = 9.0 / 16.0;
    
    if (fabs(screenRatio - targetRatio) < epsilon) {
        // 屏幕比例接近 9:16
        title3LabelT = 20;
        nextBtnT = 16;
    }
    if (@available(iOS 11.0, *)) {
        UIWindow *window = UIApplication.sharedApplication.keyWindow;
        UIEdgeInsets safeAreaInsets = window.safeAreaInsets;

        if (safeAreaInsets.top == 0 && safeAreaInsets.bottom == 0) {
            // 设备没有安全区域（没有刘海或底部安全区）
            title3LabelT = 20;
            nextBtnT = 16;
        }
    }
    [self imy_addThemeChangedBlock:^(UIView *weakObject) {
        if (IMYPublicAppHelper.shareAppHelper.isNight) {
            weakObject.backgroundColor = [UIColor imy_colorForKey:kCK_White_ANP];
        } else {
            weakObject.backgroundColor = [UIColor imy_colorForKey:@"#FFFCFD"];
        }
    }];
    
    //背景
    [self addSubview:self.topBg2ImageView];
    [self.topBg2ImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.mas_equalTo(0);
        make.height.mas_equalTo(top_bg_img_height);
    }];
    [self addSubview:self.topBgImageView];
    [self.topBgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(20);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(top_bg_img_height);
    }];
    
    [self addSubview:self.leftBgImageView];
    CGFloat left_bg_img_bottom = 34;
    [self.leftBgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(0);
        make.bottom.mas_equalTo(-left_bg_img_bottom);
        make.width.mas_equalTo(SCREEN_By375(253));
        make.height.mas_equalTo(SCREEN_By375(323));
    }];
    
    [self addSubview:self.rightBgImageView];
    [self.rightBgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(0);
        make.top.mas_equalTo(SCREEN_By375(150));
        make.width.mas_equalTo(SCREEN_By375(269));
        make.height.mas_equalTo(SCREEN_By375(322));
    }];
    
    //信息
    [self addSubview:self.title3Label];
    [self.title3Label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.mas_equalTo(self.topBgImageView.mas_bottom).offset(title3LabelT);
        make.height.mas_equalTo(21);
    }];
    
    
    [self addSubview:self.centerImageView];
    [self.centerImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(self.title3Label.mas_bottom).offset(8);
        make.height.mas_equalTo(center_img_height);
    }];
    
    [self addSubview:self.nextBtn];
    [self.nextBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(40);
        make.height.mas_equalTo(48);
        make.right.mas_equalTo(-40);
        make.top.mas_equalTo(self.centerImageView.mas_bottom).offset(nextBtnT);
    }];
    
    //关闭按钮
    IMYButton *closeButton = [IMYButton new];
    closeButton.imageAtDirection = IMYDirectionUp;
    [closeButton addTarget:self action:@selector(close) forControlEvents:UIControlEventTouchUpInside];
    
    @weakify(closeButton);
    [self imy_addThemeChangedBlock:^(id weakObject) {
        @strongify(closeButton);
        
        BOOL isNight = [IMYPublicAppHelper shareAppHelper].isNight;
        
        NSString *imageName;
        if (isNight) {
            imageName = @"icon_pop_close_yejian";
        } else {
            imageName = @"icon_pop_close";
        }
        
        IMYIcon *icon = [IMYIcon iconWithImage:[UIImage imageNamed:imageName]];
        icon.size = CGSizeMake(20, 20);
        closeButton.rightIcon = icon;
    }];
    
    [self addSubview:closeButton];
    @weakify(self);
    [closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.width.height.equalTo(@20);
        make.right.equalTo(self).offset(-16);
        make.top.mas_offset(16);
    }];
    
  
}

- (void)setType:(IMYVKUserMode)type{
    _type = type;
    [self setupView];
}

- (void)setupView{
    //设置内容
    if (self.type == IMYVKUserModeLama) {  //育儿
        [self.topBgImageView imy_setImage:@"pop_bg_top_yuer"];
        [self.centerImageView imy_setImage:@"pop_bg_middle_yuer"];
        self.title3Label.text = @"育儿模式下，美柚将为你提供专属服务：";
        [self.nextBtn setTitle:IMYString(@"开启育儿模式") forState:UIControlStateNormal];
        //877 取消 "下一步" 按钮变化
//        [self updateLamaUI];
    }else if (self.type == IMYVKUserModePregnancy){  //怀孕
        [self.topBgImageView imy_setImage:@"pop_bg_top_huaiyun"];
        [self.centerImageView imy_setImage:@"pop_bg_middle_huaiyun"];
        self.title3Label.text = @"怀孕模式下，美柚将为你提供专属服务：";
        [self.nextBtn setTitle:IMYString(@"开启怀孕模式") forState:UIControlStateNormal];
    }else if (self.type == IMYVKUserModeForPregnant){ //备孕
        [self.topBgImageView imy_setImage:@"pop_bg_top_beiyun"];
        [self.centerImageView imy_setImage:@"pop_bg_middle_beiyun"];
        self.title3Label.text = @"备孕模式下，美柚将为你提供专属服务：";
        [self.nextBtn setTitle:IMYString(@"开启备孕模式") forState:UIControlStateNormal];
    }else{//经期
        [self.topBgImageView imy_setImage:@"pop_bg_top_jinqi"];
        [self.centerImageView imy_setImage:@"pop_bg_middle_jinqi"];
        self.title3Label.text = @"经期模式下，美柚将为你提供专属服务：";
        [self.nextBtn setTitle:IMYString(@"开启经期模式") forState:UIControlStateNormal];
    }
}


- (void)nextBtnClickAction{
    if ([IMYPublicAppHelper shareAppHelper].useYoungMode) {
        NSArray *toModeNameArray = @[IMYString(@"经期"), IMYString(@"怀孕"), IMYString(@"备孕"), IMYString(@"育儿")];
        NSString *toModeName = [toModeNameArray imy_objectAtIndex:self.type];
        NSString *summary = [NSString stringWithFormat:IMYString(@"你正在青少年模式中，暂时无法开启%@模式"), toModeName];
        [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消") otherTitles:@[IMYString(@"关闭青少年模式")] summary:summary showInView:nil action:^(NSInteger index) {
            NSInteger entrance = 0;
            if ([self.fromName isEqualToString:@"recordSelect"]) {
                entrance = 27;
            } else if ([self.fromName isEqualToString:@"mytabSelect"]) {
                entrance = 28;
            }
            
            if (index == 1) {
                [self closeYouthModeActionSheetClickAnalytics:entrance
                                                   publicType:@"点击青少年模式按钮" ];
                if ([IMYNetState networkEnable]) {
                    !self.willNextActionBlock?:self.willNextActionBlock(self.nextBtn);
                }
                
                //关闭青少年模式
                /// 27：首页_记录页面切换身份
                /// 28：我的tab_切换身份
                void (^finishBlock)(NSDictionary *info) = ^void(NSDictionary *info) {
                    imy_asyncMainBlock(^{
                        NSInteger result_key = [info[@"result_key"] integerValue];
                        NSString *debugToast = @"";
                        if (result_key == 1) {
                            !self.nextActionBlock?:self.nextActionBlock(self.nextBtn);
                        } else if (result_key == 3) {
                            // 如果是取消操作要销毁我 tab 页面的 modeInfoView，否则会无法切换身份
                            [[NSNotificationCenter defaultCenter] postNotificationName:@"K_freeModeInfoView" object:nil];
                        }
                    });
                };
                
                NSMutableDictionary *params = [NSMutableDictionary dictionary];
                [params imy_setNonNilObject:finishBlock forKey:@"finishBlock"];
                [params imy_setNonNilObject:@(entrance) forKey:@"entrance"];
                //模拟调用 result_key: 1 成功, 2 失败, 3 取消
                //[params imy_setNonNilObject:@(1) forKey:@"result_key"];
                [[IMYURIManager sharedInstance] runActionWithPath:@"youthmode/deactivate" params:params info:nil];
            } else {
                //取消
                [self closeYouthModeActionSheetClickAnalytics:entrance
                                                   publicType:@"取消"];
            }
        }];
        
        NSInteger entrance = 0;
        if ([self.fromName isEqualToString:@"recordSelect"]) {
            entrance = 27;
        } else if ([self.fromName isEqualToString:@"mytabSelect"]) {
            entrance = 28;
        }
        [self closeYouthModeActionSheetExposuredAnalytics:entrance];
        
    } else {
        !self.willNextActionBlock?:self.willNextActionBlock(self.nextBtn);
        !self.nextActionBlock?:self.nextActionBlock(self.nextBtn);
    }
}


/// 场景1:当用户无已出生宝宝
/// 场景2:当用户是怀孕模式且有已出生宝宝
- (void)updateLamaUI {
    
    @weakify(self);
    imy_asyncBlock(^{
        @strongify(self);
        
        BOOL hasBaby = NO;
#if __has_include(<IMYRecord/IMYRecordBabyManager.h>)
        hasBaby = [[IMYRecordBabyManager sharedInstance] babyList].count > 0;
#endif
        imy_asyncMainBlock(^{
            @strongify(self);
            IMYVKUserMode userMode = [IMYPublicAppHelper shareAppHelper].userMode;
            if (!hasBaby || (userMode == IMYVKUserModePregnancy && hasBaby)) {
                if ([self.fromName isEqualToString:@"recordSelect"]) {
                    [self.nextBtn setTitle:IMYString(@"下一步") forState:UIControlStateNormal];
                }
            }
        });
    });
}

#pragma mark - UI


- (UIView *)topView {
    if (!_topView) {
        _topView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 52)];
        [_topView addSubview:self.titleLabel];
    }
    return _topView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 30 , 28)];
        _titleLabel.textColor = [UIColor imy_colorForKey:kCK_Black_A];
        _titleLabel.font = [UIFont boldSystemFontOfSize:20];
    }
    return _titleLabel;
}

- (UIImageView *)topBg2ImageView{
    if (!_topBg2ImageView) {
        UIImageView *imgView = [UIImageView new];
        imgView.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_WIDTH*(9.0/16.0));
        
        CAGradientLayer *gradientLayer = [CAGradientLayer new];
        gradientLayer.colors = @[
            (id)[[UIColor imy_colorForKey:@"#474747"] colorWithAlphaComponent:1].CGColor,
            (id)[[UIColor imy_colorForKey:@"#474747"] colorWithAlphaComponent:0].CGColor,
        ];
        gradientLayer.locations = @[@0, @1];
        gradientLayer.startPoint = CGPointMake(0.5, 0);
        gradientLayer.endPoint = CGPointMake(0.5, 1);
        gradientLayer.frame = imgView.bounds;
        [imgView.layer addSublayer:gradientLayer];
        
        [imgView imy_addThemeChangedBlock:^(UIImageView *weakObject) {
            if (IMYPublicAppHelper.shareAppHelper.isNight) {
                weakObject.image = nil;
                gradientLayer.hidden = NO;
            } else {
                weakObject.image = [UIImage imy_imageForKey:@"pop_bg_top"];
                gradientLayer.hidden = YES;
            }
        }];
        _topBg2ImageView = imgView;
    }
    return _topBg2ImageView;
}


- (UIImageView *)topBgImageView{
    if (!_topBgImageView) {
        UIImageView *imgView = [UIImageView new];
        _topBgImageView = imgView;
    }
    return _topBgImageView;
}

- (UIImageView *)leftBgImageView{
    if (!_leftBgImageView) {
        UIImageView *imgView = [UIImageView new];
        imgView.image = [UIImage imy_imageForKey:@"pop_bg_img_one"];
        _leftBgImageView = imgView;
    }
    return _leftBgImageView;
}


- (UIImageView *)rightBgImageView{
    if (!_rightBgImageView) {
        UIImageView *imgView = [UIImageView new];
        imgView.image = [UIImage imy_imageForKey:@"pop_bg_img_two"];
        _rightBgImageView = imgView;
    }
    return _rightBgImageView;
}



- (UILabel *)title3Label{
    if (!_title3Label) {
        UILabel *lab = [UILabel new];
        [lab imy_setTextColor:kCK_Black_B];
        lab.font = [UIFont imy_regularWith:15];
        _title3Label = lab;
    }
    return _title3Label;
}

- (UIImageView *)centerImageView{
    if (!_centerImageView) {
        UIImageView *imgView = [UIImageView new];
        _centerImageView = imgView;
    }
    return _centerImageView;
}

- (IMYCKLoadingTextButton *)nextBtn{
    if (!_nextBtn) {
        IMYCKLoadingTextButton *btn = [[IMYCKLoadingTextButton alloc] init];
        btn.titleLabel.font = [UIFont boldSystemFontOfSize:17];
        btn.layer.cornerRadius = 24;
        btn.layer.masksToBounds = YES;
        [btn setTitle:IMYString(@"下一步") buttonType:IMYCKLoadingButtonRedType];
        btn.titleColor = nil;
        btn.contentColor = nil;
        btn.borderColor = nil;
        
        [btn imy_setTitleColor:kCK_White_A];
        [btn imy_setBackgroundColor:kCK_Red_B];
        btn.indicatorView.activityIndicatorViewStyle = UIActivityIndicatorViewStyleWhite;
        
        [btn addTarget:self action:@selector(nextBtnClickAction) forControlEvents:UIControlEventTouchUpInside];
        _nextBtn = btn;
    }
    return _nextBtn;
}

@end


@implementation IMYModeWelcomeContentView (BiAnalytics)

- (void)closeYouthModeActionSheetExposuredAnalytics:(NSInteger)entrance {
    /*
     27：首页_记录页面切换身份
     28：我的tab_切换身份
     */
    NSDictionary *params = @{
        @"event" : @"wd_qsnms_kg",
        @"action": @1,
        @"entrance": @(entrance),
        @"public_info": @"关闭青少年模式",
        @"position": @(158)
    };
    [IMYGAEventHelper postWithPath:@"event"
                            params:params
                           headers:nil
                         completed:nil];
}



- (void)closeYouthModeActionSheetClickAnalytics:(NSInteger)entrance
                                     publicType:(NSString *)publicType {
    
    /*
     27：首页_记录页面切换身份
     28：我的tab_切换身份
     */
    
    NSDictionary *params = @{
        @"event" : @"wd_qsnms_kg",
        @"action": @2,
        @"entrance": @(entrance),
        @"public_type": publicType,
        @"public_info": @"关闭青少年模式",
        @"position": @(158)
    };
    [IMYGAEventHelper postWithPath:@"event"
                            params:params
                           headers:nil
                         completed:nil];
}

@end
