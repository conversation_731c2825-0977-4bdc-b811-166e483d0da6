//
//  SYUserModeChangeAction.m
//  ZZIMYMain
//
//  Created by lxb on 2020/12/8.
//

#import "SYUserModeChangeAction.h"
#import <IMYBaseKit/IMYPublic.h>
#import <IMYBaseKit/IMY_ViewKit.h>

#if __has_include(<IMYRecord/IMYRecord.h>)
#import <IMYRecord/IMYRecord.h>
#import "IMYRecordPregnancyBabyManager.h"
#endif


#if __has_include("SYBabyReserveSheetView.h")
#import "SYBabyReserveSheetView.h"
#endif
#import "SYUserHelper.h"
#import "IMYMeGlobalMacros.h"
//MARK: - SYPregnancyToLama
@interface SYUserModeChangeAction()

@end

@implementation SYUserModeChangeAction

#pragma mark - Pregnacy To Lama

+ (void)checkPregnancyToLamaMode:(SYUserModeChangeActionBlk)completionBlk{
    NSInteger pregnancyDays = 0;
#if __has_include(<IMYRecord/IMYRecord.h>)
    pregnancyDays = [IMYDayRecordModel getPregnancyStartDayDiffForParam];
#endif
   if (pregnancyDays < 140){
        [self handlePregnacyLessThan140Days:pregnancyDays completion:completionBlk];
    }else{
        [self handlePregnacyGreaterThanOrEqualTo140Days:pregnancyDays completion:completionBlk];
    }
}

+ (void)checkPregnancyForWelcomeToLamaMode:(IMYCKLoadingTextButton *)loadingBtn completionBlk:(SYUserModeChangeActionBlk)completionBlk{
    NSInteger pregnancyDays = 0;
#if __has_include(<IMYRecord/IMYRecord.h>)
    pregnancyDays = [IMYDayRecordModel getPregnancyStartDayDiffForParam];
#endif
   if (pregnancyDays < 140){
        [self handleWelcomePregnacyLessThan140Days:pregnancyDays loadingBtn:loadingBtn completion:completionBlk];
    }else{
        [self handlePregnacyGreaterThanOrEqualTo140Days:pregnancyDays completion:completionBlk];
    }
}


/// 获取预产期日期 xxxx.x.x格式
+ (NSString *)getDotPregnancyDateString{
    NSDate *date = nil;
#if __has_include(<IMYRecord/IMYRecord.h>)
    date = [[IMYCalendarUserHelper sharedHelper].pregnancy imy_getOnlyDate];
#endif
    if (!date) {
        return @"";
    }
    NSString *dateString = [date imy_getOnlyShortDateString];
    NSString *strUrl = [dateString stringByReplacingOccurrencesOfString:@"-" withString:@"."];
    return strUrl;
}

//MARK: - 小于140天的逻辑
+ (void)handlePregnacyLessThan140Days:(NSInteger)pregnancyDays completion:(SYUserModeChangeActionBlk)completionBlk{
#if __has_include(<IMYRecord/IMYRecord.h>)
    BOOL hasBaby = [IMYPublicAppHelper shareAppHelper].babyCount > 0;
    NSString *dueDateString = [self getDotPregnancyDateString];
    if (!hasBaby) {
        NSString *message = [NSString stringWithFormat:IMYString(@"当前孕期不满140天，请先创建宝宝后开始育儿模式。开始后，将删除当前孕期（预产期：%@）"), dueDateString];
        IMYActionMessageBox *box = [IMYActionMessageBox showBoxWithTitle:IMYString(@"暂无宝宝") message:message style:IMYMessageBoxStyleFlatten action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
            [messageBox dismiss];
            if (sender == messageBox.leftButton) {
                !completionBlk?:completionBlk(NO, NO,nil);
                return ;
            }
            ///< 放到开启育儿模式前删除记录,不然会造成身份异常.
            ///< 参考 `turnOnLamaModeWithBabyBirthday`
//            [IMYDayRecordModel deleteCurrentPregnancy];
            !completionBlk?:completionBlk(YES, YES,nil);
        }];
        [box.rightButton imy_setTitle:IMYString(@"创建宝宝")];
        return ;
    }
    
    if (pregnancyDays <= 28) {
        NSString *message = [NSString stringWithFormat:IMYString(@"开始育儿模式，将删除小于28天的孕期记录（预产期：%@）"), dueDateString];
        [IMYActionMessageBox showBoxWithTitle:IMYString(@"确定开始育儿模式吗？")
                                      message:message style:IMYMessageBoxStyleFlat
                            isShowCloseButton:NO
                                textAlignment:NSTextAlignmentCenter
                            cancelButtonTitle:IMYString(@"取消")
                             otherButtonTitle:IMYString(@"确定")
                                       action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
            [messageBox dismiss];
            if (sender == messageBox.leftButton) {
                !completionBlk?:completionBlk(NO, NO,nil);
                return ;
            }
            [UIWindow imy_showLoadingHUD];
            [IMYRecordPregnancyBabyManager deleteGestation:YES loadingBtn:nil completeBlock:^(NSError * _Nullable error) {
                if (!error) {
                    [IMYDayRecordModel deleteCurrentPregnancy];
                    [self handleSelectedLastOrLastBaby];
                    [self lamaClickGAEventName:@"hyqhqtsf_ytgx"];
                    !completionBlk?:completionBlk(YES, NO,nil);
                }

            }];

        }];
    } else {
        NSString *summary = [NSString stringWithFormat:IMYString(@"请管理当前孕期记录（预产期%@），\n操作后将开始育儿模式"), dueDateString];
        [self deleteOrStopPregnancyWithSummary:summary completion:completionBlk];
    }
#endif
}

+ (void)handleWelcomePregnacyLessThan140Days:(NSInteger)pregnancyDays loadingBtn:(IMYCKLoadingTextButton *)loadingBtn completion:(SYUserModeChangeActionBlk)completionBlk{
#if __has_include(<IMYRecord/IMYRecord.h>)
    BOOL hasBaby = [IMYPublicAppHelper shareAppHelper].babyCount > 0;
    NSString *dueDateString = [self getDotPregnancyDateString];
    if (!hasBaby) {
        NSString *summary = [NSString stringWithFormat:IMYString(@"添加宝宝后可开始育儿模式。开始模式后，将删除当前未满140天的孕期记录（预产期：%@）"), dueDateString];
        [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消") otherTitles:@[IMYString(@"添加宝宝")] summary:summary showInView:nil action:^(NSInteger index) {
            if (index) {
                ///< 放到开启育儿模式前删除记录,不然会造成身份异常.
                ///< 参考 `turnOnLamaModeWithBabyBirthday`
    //            [IMYDayRecordModel deleteCurrentPregnancy];
                !completionBlk?:completionBlk(YES, YES,nil);
            }else{//取消
                !completionBlk?:completionBlk(NO, NO,nil);
            }
        }];
        return ;
    }
    
    if (pregnancyDays <= 28) {
        NSString *summary = [NSString stringWithFormat:IMYString(@"请管理当前小于28天的孕期记录（预产期：%@），\n操作后将开始育儿模式"), dueDateString];
        [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消") otherTitles:@[IMYString(@"删除此次孕期")] summary:summary showInView:nil action:^(NSInteger index) {
            if (index) {
                @weakify(self)
                [IMYRecordPregnancyBabyManager deleteGestation:NO loadingBtn:loadingBtn completeBlock:^(NSError * _Nullable error) {
                    @strongify(self)
                    if (!error) {
                        [IMYDayRecordModel deleteCurrentPregnancy];
                        [self handleSelectedLastOrLastBaby];
                        !completionBlk?:completionBlk(YES, NO,nil);
                    }
                } ];
     
                [self lamaClickGAEventName:@"hyqhqtsf_ytgx"];
            }else{//取消
                !completionBlk?:completionBlk(NO, NO,nil);
            }
        }];
    } else {
        NSString *summary = [NSString stringWithFormat:IMYString(@"请管理当前孕期记录（预产期%@），\n操作后将开始育儿模式"),dueDateString];
        [self deleteOrStopPregnancyWithSummary:summary completion:completionBlk];
    }
#endif
}


/// 删除或者终止妊娠
/// @param summary 标题
/// @param completionBlk 完成回调
+ (void)deleteOrStopPregnancyWithSummary:(NSString *)summary completion:(SYUserModeChangeActionBlk)completionBlk{
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSString *dueDateString = [self getDotPregnancyDateString];
    @weakify(self);
    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消") destructiveTitle:IMYString(@"删除此次孕期") otherTitles:@[IMYString(@"妊娠终止")] summary:summary showInView:nil action:^(NSInteger index) {
        @strongify(self);
        if (index == 2) { ///< 删除此次孕期
            [self lamaClickGAEventName:@"hyqhqtsf_scccyq"];
            [self bi_reportWithAction:2 event:@"yy_sy_yqjstc" public_type:@"删除此次孕期"];

            [IMYRecordPregnancyBabyManager deleteGestation:NO loadingBtn:nil completeBlock:^(NSError * _Nullable error) {
                @strongify(self);
                if (!error) {
                    ///1. 删除孕期记录
                    [IMYDayRecordModel deleteCurrentPregnancy];
                    /// 2.选中最小的宝宝
                    [self handleSelectedLastOrLastBaby];
                    //修改了宝宝数据，及时同步数据
                    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationRecordUploadDataImmediately object:nil];
                    !completionBlk?:completionBlk(YES, NO,nil);
                }
            }];
        }else if(index == 1){///< 妊娠终止
            [self bi_reportWithAction:2 event:@"yy_sy_yqjstc" public_type:@"妊娠终止"];

            [self handleFinishCurrentPregnacy:dueDateString completion:^(BOOL result, BOOL needAdd,NSArray *deleteBabyList) {
                if (result) {
                    [self lamaClickGAEventName:@"hyqhqtsf_rszz"];
                    //修改了宝宝数据，及时同步数据
                    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationRecordUploadDataImmediately object:nil];
                    
                }
                !completionBlk?:completionBlk(result, needAdd,nil);
            }];
        }else{//取消
            [self bi_reportWithAction:2 event:@"yy_sy_yqjstc" public_type:@"取消"];
            !completionBlk?:completionBlk(NO, NO,nil);
        }
    }];
    [self bi_reportWithAction:1 event:@"yy_sy_yqjstc" public_type:@""];

#endif
}

/// 删除当前记录并选中之前的宝宝,如果之前的没有,则最小宝宝
//+ (void)handleSelectedLastBabyWithAddNew:(BOOL)addNew completion:(void(^)())completionBlk{
//#if __has_include(<IMYRecord/IMYRecord.h>)
//    if (!addNew) {///有新增,默认就是用新宝宝
//        ///1.上次有选中的
//        ///2.没有选中的,取最小的
//        [self handleSelectedLastOrLastBaby];
//    }
//    //同步数据
//    [[IMYRecordBabyDataAutoUploadManager sharedManager] uploadToCloud:^(BOOL success, NSError *error) {
//         [[IMYRecordBabyManager sharedInstance] updateBabyList];
//        [[NSNotificationCenter defaultCenter] postNotificationName:@"kSYUserBabyInfoChangedNotification" object:[NSDictionary dictionary]];
//        imy_asyncMainBlock(^{
//            !completionBlk?:completionBlk();
//        });
//    }];
//#endif
//}

+ (void)handleFinishCurrentPregnacy:(NSString *)dueDateString completion:(SYUserModeChangeActionBlk)completionBlk{
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSDate *minDate = [[IMYPregnanceModel getLastPregnancyModel].startDate dateByAddingDays:15];
    NSDate *maxDate = [NSDate imy_today];
    NSDate *endDate = [[IMYPregnanceModel getLastPregnancyModel].startDate dateByAddingDays:294];
    if ([maxDate isLaterThanDate:endDate]) {
        maxDate = endDate;//预产期大于结束日期，取预产期+294
    }
    if ([minDate isLaterThanDate:maxDate]) {
        minDate = [NSDate imy_today];
    }
    @weakify(self);
    IMYPickerView *pickerView = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES dataArray:@[minDate, maxDate] pickerViewTpe:IMYPickerViewTypeDate confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
        NSDate *finshedDate = result.resultDate;
        @strongify(self);
        [IMYRecordPregnancyBabyManager suspendGestationWithDueDate:finshedDate loadingBtn:nil completeBlock:^(NSError * _Nullable error) {
            @strongify(self);
            if (!error) {
                [IMYDayRecordModel endCurrentPregnancy:finshedDate];
                [self handleSelectedLastOrLastBaby];
                !completionBlk?:completionBlk(YES, NO,nil);
            }
        }];

    } cancelBlock:^{
        !completionBlk?:completionBlk(NO, NO,nil);
    }];

    pickerView.title = IMYString(@"妊娠终止日期");
    [pickerView setSelectWithDate:maxDate];
    [pickerView show];
#endif
}

//MARK: - 大于等于140天的逻辑
+ (void)handlePregnacyGreaterThanOrEqualTo140Days:(NSInteger)pregnacyDays completion:(SYUserModeChangeActionBlk)completionBlk{
    
    @weakify(self);
    IMYActionSheet *actionSheet = [[IMYActionSheet alloc] initWithWithCancelTitle:IMYString(@"取消") destructiveTitle:nil otherTitles:@[IMYString(@"宝宝出生了"), IMYString(@"孕期结束")] summary:nil showInView:nil];
    actionSheet.destructiveButtonIndex = 1;
    actionSheet.onActionBlock = ^(NSInteger index) {
        @strongify(self);
        if (index == 1) { ///< 宝宝出生了
            !completionBlk?:completionBlk(YES, YES,nil);
        }else if(index == 2){///< 孕期结束
            [self deleteOrStopPregnancyWithSummary:nil completion:completionBlk];
        }else{//取消
            !completionBlk?:completionBlk(NO, NO,nil);
        }
    };
    [actionSheet show];
}
//MARK: - 大于等于294天的逻辑
+ (void)handlePregnacyGreaterThanOrEqualTo294DaysWithCompletion:(SYUserModeChangeActionBlk)completionBlk{
#if __has_include(<IMYRecord/IMYRecord.h>)
    @weakify(self);
    IMYActionSheet *actionSheet = [[IMYActionSheet alloc] initWithWithCancelTitle:IMYString(@"取消") destructiveTitle:nil otherTitles:@[IMYString(@"宝宝还没出生，修改预产期"), IMYString(@"孕期结束")] summary:@"请选择你的孕期状态" showInView:nil];
//    actionSheet.destructiveButtonIndex = 1;
    actionSheet.onActionBlock = ^(NSInteger index) {
        @strongify(self);
        if (index == 1) { ///< 宝宝还没出生，修改预产期
            [[IMYURIManager shareURIManager] runActionWithString:@"changeMode/modifyPregnacy/showConfirmVC"];
            [self bi_reportWithAction:2 event:@"yy_sy_yqzttc" public_type:@"宝宝还没出生"];

            !completionBlk?:completionBlk(YES, YES,nil);
        }else if(index == 2){///< 孕期结束
            [self bi_reportWithAction:2 event:@"yy_sy_yqzttc" public_type:@"孕期结束"];

            [self deleteOrStopPregnancyWithSummary:nil completion:^(BOOL result, BOOL needAdd, NSArray * _Nonnull deleteBabyList) {
                @strongify(self);
                if (result) {
                    if ([IMYRecordBabyManager sharedInstance].babyList.count > 0) {
                        [SYUserHelper sharedHelper].userModelType = SYUserModelTypeLama;
                    } else {
                        [SYUserHelper sharedHelper].userModelType = SYUserModelTypeNormal;
                    }
                    [[SYUserHelper sharedHelper] saveToDB];
                    [[IMYCalendarUserHelper sharedHelper] recalculateUserInfo];
                    IMY_POST_NOTIFY(ChangedUserModeNotification);
                }
                !completionBlk?:completionBlk(result, NO,nil);
            }];
        }else{//取消
            [self bi_reportWithAction:2 event:@"yy_sy_yqzttc" public_type:@"取消"];
            !completionBlk?:completionBlk(NO, NO,nil);
        }
    };
    [actionSheet show];
    [self bi_reportWithAction:1 event:@"yy_sy_yqzttc" public_type:@""];
#endif
}
/*
 1：曝光
 2：点击
 */
+ (void)bi_reportWithAction:(NSInteger)action event:(NSString *)event public_type:(NSString *)public_type{
    NSDictionary *params = @{@"event":event,
                             @"action":@(action)};
    if (!imy_isEmptyString(public_type)) {
        params = [[NSMutableDictionary alloc] initWithDictionary:params];
        [params setValue:public_type forKey:@"public_type"];
    }
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}



//MARK: - 身份切换页面检查宝宝个数
/// 开启育儿模式
/// @param birthday 宝宝生日
/// @param completionBlk 完成回调
//+ (void)turnOnLamaModeWithBabyBirthday:(NSDate *)birthday completion:(SYUserModeChangeActionBlk)completionBlk{
//#if __has_include(<IMYRecord/IMYRecord.h>)
//    NSString *birthdayString = [birthday imy_getOnlyDateString];
//    
//    /// 1. 宝宝个数超上限
//    if ([IMYRecordBabyManager sharedInstance].babyList.count >= 5) {
//        [IMYActionMessageBox showBoxWithTitle:IMYString(@"宝宝已达上限") message:IMYString(@"添加新的宝宝前请至少删除一个宝宝") style:IMYMessageBoxStyleFlatten action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
//            [messageBox dismiss];
//            if (sender == messageBox.rightButton) { ///< 跳转宝宝页面,进行删除操作.
//                IMYURI *uri = [IMYURI uriWithPath:@"user/baby/list" params:@{@"location_id" : @(2)} info:nil];
//                [IMYURIManager.shareURIManager runActionWithURI:uri];
//            }
//            !completionBlk?:completionBlk(NO, NO,nil);
//        }];
//        return ;
//    }
//    ///< 小于140天且无宝宝跳转到创建宝宝,要在此刻删除孕期记录,过早删除会导致身份异常
//    ///< 参考 `handlePregnacyLessThan140Days`
//    BOOL noBabyAndPregnancyDayLessThan140Days = NO;
//    NSInteger pregnancyDay = [self getPregnancyStartDayDiff:birthday];
//    NSInteger pregnancyDays2 = [IMYDayRecordModel getPregnancyStartDayDiffForParam];
//    if (pregnancyDays2 < 140){
//        if ([IMYRecordBabyManager sharedInstance].babyList.count == 0 && pregnancyDay >= 0 && pregnancyDay < 140) {
//            [IMYDayRecordModel deleteCurrentPregnancy];
//            noBabyAndPregnancyDayLessThan140Days = YES;
//        }
//    }
//    
//    /// 2.无效的出生日
//    pregnancyDay = [self getPregnancyStartDayDiff:birthday];
//    if ((pregnancyDay >= 0 && pregnancyDay < 140) || pregnancyDay > 296) {
//        IMYActionMessageBox *box = [IMYActionMessageBox showBoxWithTitle:IMYString(@"出生日期异常") message:IMYString(@"选择的出生日期不在妊娠周期内，请重新设置出生日期或预产期") style:IMYMessageBoxStyleFlatten action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
//            [messageBox dismiss];
//            !completionBlk?:completionBlk(NO, NO,nil);
//        }];
//        box.singleButton = YES;
//        [box.rightButton imy_setTitle:IMYString(@"我知道了")];
//        return ;
//    }
//    
//    @weakify(self);
//    void(^block)(BOOL needAdd, NSArray * _Nonnull babyList) = ^(BOOL needAdd, NSArray * _Nonnull babyList){
//        @strongify(self);
//        /// 1.有删除的
//        for (IMYRecordBabyModel *babyModel in babyList) {
//            [[IMYRecordBabyManager sharedInstance] deleteBaby:babyModel.baby_id];
//        }
//        /// 2.选中最小的宝宝
//        if (!needAdd) {
//            [self handleSelectedLastOrLastBaby];
//            [IMYDayRecordModel deleteCurrentPregnancy];///不需要新增宝宝,需要删除当前孕期
//        }
//        
////        if (needAdd){///< 不区分是否是新建的宝宝,还是选中已有的宝宝
//            [self lamaClickGAEventName:@"hyqhqtsf_bbcsl"];
////        }
//        !completionBlk?:completionBlk(YES, needAdd,nil);
//    };
//    NSString *dotBirthdayString = [birthdayString stringByReplacingOccurrencesOfString:@"-" withString:@"."];
//    /// 3. 有相同时间段的宝宝, 不区分性别
//#if __has_include("SYBabyReserveSheetView.h")
//    if ([SYBabyReserveSheetView containBabySameBirthday:birthdayString]) {
//        NSString *contentText = [NSString stringWithFormat:IMYString(@"新添加的宝宝（%@）与已有宝宝生日相同，请选择你要保留的宝宝。\n选择后，系统将按对应宝宝开始育儿模式，未保留的宝宝，其宝宝记录及亲友关系将被删除且不可恢复。（未保留“新添加的宝宝”，将删除大事记及非大肚照记录）。"), dotBirthdayString];
//        IMYRecordBabyModel *newBabyModel = [[IMYRecordBabyModel alloc] init];
//        newBabyModel.birthday = birthdayString;
//        NSArray *babyList = [SYBabyReserveSheetView babyListSameBirthday:birthdayString];
//        [SYBabyReserveSheetView showSheetViewWithBabyModel:newBabyModel babyList:babyList content:contentText completeBlock:block];
//        return ;
//    }
//#endif
//    
//    void(^doneAction)(void) = ^{
//        if (noBabyAndPregnancyDayLessThan140Days) {
//            NSDictionary *dict = @{@"event": @"hyqhqtsf_cjbb", @"action": @2};
//            [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
//        }else{
//            [self lamaClickGAEventName:@"hyqhqtsf_bbcsl"];
//        }
//        !completionBlk?:completionBlk(YES, YES,nil);
//    };
//#if __has_include("SYBabyReserveSheetView.h")
//    /// 4.1 有相似宝宝, 1-4天内
//     if ([SYBabyReserveSheetView hasSimilarBabyBetween0And4:birthdayString]) {
//         [SYBabyReserveSheetView showSimilarBabyAlertWithBirthday:birthdayString completion:^(BOOL addNew) {
//             if (!addNew) {
//                 return ;
//             }
//             doneAction();
//         }];
//         return ;
//    }
//    
//    /// 4.2 与已有宝宝近似, 宝宝生日间隔在 [4-154] 天
//    if ([SYBabyReserveSheetView containBabyBetween4And154:birthdayString]) {
//        NSString *contentText = [NSString stringWithFormat:IMYString(@"新添加的宝宝（出生日%@）与已有宝宝生日间隔在4-154天内，存在冲突，请选择你要保留的宝宝。\n选择后，系统将按对应宝宝开始育儿模式，未保留的宝宝，其宝宝记录及亲友关系将被删除且不可恢复（未保留“新添加的宝宝”，会删除当前孕期）"), dotBirthdayString];
//        IMYRecordBabyModel *newBabyModel = [[IMYRecordBabyModel alloc] init];
//        newBabyModel.birthday = birthdayString;
//        NSArray *babyList = [SYBabyReserveSheetView babyListBetween4And154:birthdayString];
//        [SYBabyReserveSheetView showSheetViewWithBabyModel:newBabyModel babyList:babyList content:contentText completeBlock:block];
//        return ;
//    }
//#endif
//    /// 5. 与已有的宝宝间隔小于4天, 返回孕期数据
//    /// 6. 其他情况正常添加宝宝
//    doneAction();
//#endif
//}

//MARK: - 身份切换页面检查宝宝个数  同步处理
/// 开启育儿模式
/// @param birthday 宝宝生日
/// @param completionBlk 完成回调
+ (void)sync_turnOnLamaModeWithBabyBirthday:(NSDate *)birthday completion:(SYUserModeChangeActionBlk)completionBlk{
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSString *birthdayString = [birthday imy_getOnlyDateString];
    NSInteger babyCount = [IMYRecordBabyManager sharedInstance].babyList.count;
    /// 1. 宝宝个数超上限
    if (babyCount >= 5) {
        [IMYActionMessageBox showBoxWithTitle:IMYString(@"宝宝已达上限") message:IMYString(@"添加新的宝宝前请至少删除一个宝宝") style:IMYMessageBoxStyleFlatten action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
            [messageBox dismiss];
            if (sender == messageBox.rightButton) { ///< 跳转宝宝页面,进行删除操作.
                IMYURI *uri = [IMYURI uriWithPath:@"user/baby/list" params:@{@"location_id" : @(2)} info:nil];
                [IMYURIManager.shareURIManager runActionWithURI:uri];
            }
            !completionBlk?:completionBlk(NO, NO, nil);
        }];
        return ;
    }
    
//    ///< 小于140天且无宝宝，跳转到创建宝宝,要在此刻删除孕期记录,过早删除会导致身份异常
//    ///< 参考 `handlePregnacyLessThan140Days`
//    BOOL noBabyAndPregnancyDayLessThan140Days = NO;
    NSInteger pregnancyDay = [self getPregnancyStartDayDiff:birthday];
//    if ([IMYRecordBabyManager sharedInstance].babyList.count == 0 && pregnancyDay >= 0 && pregnancyDay < 140) {
////        [IMYDayRecordModel deleteCurrentPregnancy];  //by slj
//        noBabyAndPregnancyDayLessThan140Days = YES;
//    }
    /// 2.无效的出生日
//    pregnancyDay = [self getPregnancyStartDayDiff:birthday];
    if ((pregnancyDay >= 0 && pregnancyDay < 140 && babyCount > 0) ||  pregnancyDay > 296) {
        IMYActionMessageBox *box = [IMYActionMessageBox showBoxWithTitle:IMYString(@"出生日期异常") message:IMYString(@"选择的出生日期不在妊娠周期内，请重新设置出生日期或预产期") style:IMYMessageBoxStyleFlatten action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
            [messageBox dismiss];
            !completionBlk?:completionBlk(NO, NO,nil);
        }];
        box.singleButton = YES;
        [box.rightButton imy_setTitle:IMYString(@"我知道了")];
        return ;
    }
    
    @weakify(self);
    void(^block)(BOOL needAdd, NSArray * _Nonnull babyList) = ^(BOOL needAdd, NSArray * _Nonnull babyList){
        @strongify(self);
        //没有删除的宝宝
        /// 1.有删除的
        for (IMYRecordBabyModel *babyModel in babyList) {
            babyModel.is_deleted = YES;
        }
        !completionBlk?:completionBlk(YES, needAdd,babyList);
    };
    NSString *dotBirthdayString = [birthdayString stringByReplacingOccurrencesOfString:@"-" withString:@"."];
    /// 3. 有相同时间段的宝宝, 不区分性别
#if __has_include("SYBabyReserveSheetView.h")
    if ([SYBabyReserveSheetView containBabySameBirthday:birthdayString]) {
        NSString *contentText = [NSString stringWithFormat:IMYString(@"新添加的宝宝（%@）与已有宝宝生日相同，请选择你要保留的宝宝。\n选择后，系统将按对应宝宝开始育儿模式，未保留的宝宝，其宝宝记录及亲友关系将被删除且不可恢复。（未保留“新添加的宝宝”，将删除大事记及非大肚照记录）。"), dotBirthdayString];
        IMYRecordBabyModel *newBabyModel = [[IMYRecordBabyModel alloc] init];
        newBabyModel.birthday = birthdayString;
        NSArray *babyList = [SYBabyReserveSheetView babyListSameBirthday:birthdayString];
        [SYBabyReserveSheetView showSheetViewWithBabyModel:newBabyModel babyList:babyList content:contentText completeBlock:block];
        return ;
    }
#endif
    
    void(^doneAction)(void) = ^{
        //by slj
//        if (noBabyAndPregnancyDayLessThan140Days) {
//            NSDictionary *dict = @{@"event": @"hyqhqtsf_cjbb", @"action": @2};
//            [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
//        }else{
//            [self lamaClickGAEventName:@"hyqhqtsf_bbcsl"];
//        }
        !completionBlk?:completionBlk(YES, YES,nil);
    };
#if __has_include("SYBabyReserveSheetView.h")
    /// 4.1 有相似宝宝, 1-4天内
     if ([SYBabyReserveSheetView hasSimilarBabyBetween0And4:birthdayString]) {
         [SYBabyReserveSheetView showSimilarBabyAlertWithBirthday:birthdayString completion:^(BOOL addNew) {
             if (!addNew) {
                 return ;
             }
             doneAction();
         }];
         return ;
    }
    
    /// 4.2 与已有宝宝近似, 宝宝生日间隔在 [4-154] 天
    if ([SYBabyReserveSheetView containBabyBetween4And154:birthdayString]) {
        NSString *contentText = [NSString stringWithFormat:IMYString(@"新添加的宝宝（出生日%@）与已有宝宝生日间隔在4-154天内，存在冲突，请选择你要保留的宝宝。\n选择后，系统将按对应宝宝开始育儿模式，未保留的宝宝，其宝宝记录及亲友关系将被删除且不可恢复（未保留“新添加的宝宝”，会删除当前孕期）"), dotBirthdayString];
        IMYRecordBabyModel *newBabyModel = [[IMYRecordBabyModel alloc] init];
        newBabyModel.birthday = birthdayString;
        NSArray *babyList = [SYBabyReserveSheetView babyListBetween4And154:birthdayString];
        [SYBabyReserveSheetView showSheetViewWithBabyModel:newBabyModel babyList:babyList content:contentText completeBlock:block];
        return ;
    }
#endif
    /// 5. 与已有的宝宝间隔小于4天, 返回孕期数据
    /// 6. 其他情况正常添加宝宝
    doneAction();
#endif
}

+ (void)turnOnLamaModeForWelcomeWithBabyBirthday:(NSDate *)birthday completion:(SYUserModeChangeActionBlk)completionBlk{
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSString *birthdayString = [birthday imy_getOnlyDateString];

    /// 1. 宝宝个数超上限
    if ([IMYRecordBabyManager sharedInstance].babyList.count >= 5) {
        [IMYActionMessageBox showBoxWithTitle:IMYString(@"宝宝已达上限") message:IMYString(@"添加新的宝宝前请至少删除一个宝宝") style:IMYMessageBoxStyleFlatten isShowCloseButton:NO textAlignment:NSTextAlignmentCenter cancelButtonTitle:@"取消" otherButtonTitle:@"管理宝宝" action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
            [messageBox dismiss];
            if (sender == messageBox.rightButton) { ///< 跳转宝宝页面,进行删除操作.
                IMYURI *uri = [IMYURI uriWithPath:@"user/baby/list" params:@{@"location_id" : @(2),@"hideAdd":@(YES)} info:nil];
                [IMYURIManager.shareURIManager runActionWithURI:uri];
            }
            !completionBlk?:completionBlk(NO, NO,nil);
        }];
        return ;
    }
    
    ///< 小于140天且无宝宝跳转到创建宝宝,要在此刻删除孕期记录,过早删除会导致身份异常
    ///< 参考 `handlePregnacyLessThan140Days`
    BOOL noBabyAndPregnancyDayLessThan140Days = NO;
    NSInteger pregnancyDay = [self getPregnancyStartDayDiff:birthday];
    NSInteger pregnancyDays2 = [IMYDayRecordModel getPregnancyStartDayDiffForParam];
    if (pregnancyDays2 < 140){
        if ([IMYRecordBabyManager sharedInstance].babyList.count == 0 && pregnancyDay >= 0 && pregnancyDay < 140) {
            [IMYDayRecordModel deleteCurrentPregnancy];
            noBabyAndPregnancyDayLessThan140Days = YES;
        }
    }

    /// 2.无效的出生日
    pregnancyDay = [self getPregnancyStartDayDiff:birthday];
    if ((pregnancyDay >= 0 && pregnancyDay < 140) || pregnancyDay > 296) {
        IMYActionMessageBox *box = [IMYActionMessageBox showBoxWithTitle:IMYString(@"出生日期异常") message:IMYString(@"选择的出生日期不在妊娠周期内，请重新设置出生日期或预产期") style:IMYMessageBoxStyleFlatten action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
            [messageBox dismiss];
            !completionBlk?:completionBlk(NO, NO,nil);
        }];
        box.singleButton = YES;
        [box.rightButton imy_setTitle:IMYString(@"我知道了")];
        return ;
    }
    
    void(^doneAction)(void) = ^{
        if (noBabyAndPregnancyDayLessThan140Days) {
            NSDictionary *dict = @{@"event": @"hyqhqtsf_cjbb", @"action": @2};
            [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
        }else{
            [self lamaClickGAEventName:@"hyqhqtsf_bbcsl"];
        }
        !completionBlk?:completionBlk(YES, YES,nil);
    };
//
//#if __has_include("SYBabyReserveSheetView.h")
//    /// 4.1 有相似宝宝, 1-4天内
//     if ([SYBabyReserveSheetView hasSimilarBabyBetween0And4:birthdayString]) {
//         [SYBabyReserveSheetView showSimilarBabyAlertWithBirthday:birthdayString completion:^(BOOL addNew) {
//             if (!addNew) {
//                 return ;
//             }
//             doneAction();
//         }];
//         return ;
//    }
//#endif
    /// 6. 其他情况正常添加宝宝
    doneAction();
#endif
}

/// 857 同步方法
/// - Parameters:
///   - birthday: birthday description
///   - completionBlk: completionBlk description
+ (void)sync_turnOnLamaModeForWelcomeWithBabyBirthday:(NSDate *)birthday completion:(SYUserModeChangeActionBlk)completionBlk{
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSString *birthdayString = [birthday imy_getOnlyDateString];

    /// 1. 宝宝个数超上限
    if ([IMYRecordBabyManager sharedInstance].babyList.count >= 5) {
        [IMYActionMessageBox showBoxWithTitle:IMYString(@"宝宝已达上限") message:IMYString(@"添加新的宝宝前请至少删除一个宝宝") style:IMYMessageBoxStyleFlatten isShowCloseButton:NO textAlignment:NSTextAlignmentCenter cancelButtonTitle:@"取消" otherButtonTitle:@"管理宝宝" action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
            [messageBox dismiss];
            if (sender == messageBox.rightButton) { ///< 跳转宝宝页面,进行删除操作.
                IMYURI *uri = [IMYURI uriWithPath:@"user/baby/list" params:@{@"location_id" : @(2),@"hideAdd":@(YES)} info:nil];
                [IMYURIManager.shareURIManager runActionWithURI:uri];
            }
            !completionBlk?:completionBlk(NO, NO,nil);
        }];
        return ;
    }
    
    ///< 小于140天且无宝宝跳转到创建宝宝,要在此刻删除孕期记录,过早删除会导致身份异常
    ///< 参考 `handlePregnacyLessThan140Days`
    BOOL noBabyAndPregnancyDayLessThan140Days = NO;
    NSInteger pregnancyDay = [self getPregnancyStartDayDiff:birthday];
    NSInteger pregnancyDays2 = [IMYDayRecordModel getPregnancyStartDayDiffForParam];
    if (pregnancyDays2 < 140){
        if ([IMYRecordBabyManager sharedInstance].babyList.count <= 1 && pregnancyDay >= 0 && pregnancyDay < 140) {
            [IMYDayRecordModel deleteCurrentPregnancy];
            noBabyAndPregnancyDayLessThan140Days = YES;
        }
    }

    /// 2.无效的出生日
    pregnancyDay = [self getPregnancyStartDayDiff:birthday];
    if ((pregnancyDay >= 0 && pregnancyDay < 140) || pregnancyDay > 296) {
        IMYActionMessageBox *box = [IMYActionMessageBox showBoxWithTitle:IMYString(@"出生日期异常") message:IMYString(@"选择的出生日期不在妊娠周期内，请重新设置出生日期或预产期") style:IMYMessageBoxStyleFlatten action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
            [messageBox dismiss];
            !completionBlk?:completionBlk(NO, NO,nil);
        }];
        box.singleButton = YES;
        [box.rightButton imy_setTitle:IMYString(@"我知道了")];
        return ;
    }
    
    void(^doneAction)(void) = ^{
        if (noBabyAndPregnancyDayLessThan140Days) {
            NSDictionary *dict = @{@"event": @"hyqhqtsf_cjbb", @"action": @2};
            [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
        }else{
            [self lamaClickGAEventName:@"hyqhqtsf_bbcsl"];
        }
        !completionBlk?:completionBlk(YES, YES,nil);
    };
//

    /// 6. 其他情况正常添加宝宝
    doneAction();
#endif
}

+ (NSInteger)getPregnancyStartDayDiff:(NSDate *)birthday {
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSDate *startDate = [IMYDayRecordModel getCurrentPregnancyStartDate];
    if (startDate) {
        return [startDate imy_calendarGetDayDiff:birthday];
    }
#endif
    return -1;
}


/// 选中上次,没有则取最小宝宝
+ (void)handleSelectedLastOrLastBaby{
#if __has_include(<IMYRecord/IMYRecord.h>)
    ///1.上次有选中的
    IMYRecordBabyModel *lastSelectedBaby = [IMYRecordBabyManager sharedInstance].lastSelectedBaby;
    if (lastSelectedBaby) {
        [[IMYRecordBabyManager sharedInstance] selectBaby:lastSelectedBaby.baby_id];
    }else{///2.没有选中的,取最小的
        IMYRecordBabyModel *lastModel = [[IMYRecordBabyManager sharedInstance] lastBirthdayBaby];
        [[IMYRecordBabyManager sharedInstance] selectBaby:lastModel.baby_id];
    }
#endif
}

//MARK: - GA event
+ (void)lamaClickGAEventName:(NSString *)eventName{
    if(imy_isEmptyString(eventName)){
        return ;
    }
    NSDictionary *dict = @{@"event": eventName, @"action": @2, @"public_type": @3};
    [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
}

@end
