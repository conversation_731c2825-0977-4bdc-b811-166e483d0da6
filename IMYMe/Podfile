# Uncomment the next line to define a global platform for your project
# platform :ios, '9.0'
platform :ios,'12.0'

install! 'cocoapods', :disable_input_output_paths => true, :deterministic_uuids => false

target 'IMYMe' do
  # Comment the next line if you don't want to use dynamic frameworks
  #use_frameworks!

  # Pods for IMYMe
source 'https://cdn.cocoapods.org/'
  # 图片加载框架
  pod 'libwebp', :podspec => 'https://gitlab.meiyou.com/Github-iOS/libwebp/raw/master/libwebp.podspec'
  pod 'SDWebImage', :podspec => 'https://gitlab.meiyou.com/Github-iOS/SDWebImage/raw/source/SDWebImage.podspec'
  # 动态库合集
  pod 'IMYDynamicFrameworks/Meetyou', :podspec => 'https://gitlab.meiyou.com/Github-iOS/IMYDynamicFrameworks/raw/release-8.91.0/IMYDynamicFrameworks.podspec'

  pod 'YYModel', :podspec => 'https://gitlab.meiyou.com/Github-iOS/YYModel/raw/master/YYModel.podspec'
  pod 'HPGrowingTextView', :podspec => 'https://gitlab.meiyou.com/iOS/HPGrowingTextView/raw/dev/HPGrowingTextView.podspec'
  pod 'ReactiveCocoa', :podspec => 'https://gitlab.meiyou.com/Github-iOS/ReactiveCocoa/raw/source/ReactiveCocoa.podspec'

  pod 'IMYMe', :path => './'
  # 美柚第三方Keys
  pod 'IMYKeyManager/MeetYou', :podspec => 'https://gitlab.meiyou.com/iOS/IMYKeyManager/raw/release-8.60.0/IMYKeyManager.podspec'

source 'https://gitlab.meiyou.com/iOS/imyspecs.git'

  # 底层模块
#  pod 'IOC-Protocols', :podspec => 'https://gitlab.meiyou.com/iOS/IOC-Protocols/raw/master/IOC-Protocols.podspec'
  pod 'IOC-Protocols', :path => '../IOC-Protocols'
  pod 'IMYBaseKit', :path => '../IMYBaseKit'
  
  # 业务公共层
#   pod 'IMYCommonKit', :podspec => 'https://gitlab.meiyou.com/iOS/IMYCommonKit/raw/release-8.76.0/IMYCommonKit.podspec'
  pod 'IMYCommonKit', :path => '../IMYCommonKit'
  pod 'IMYVideoPlayer', :podspec => 'https://gitlab.meiyou.com/iOS/IMYVideoPlayer/raw/release-8.60.0/IMYVideoPlayer.podspec'
  
  # 账号模块
#  pod 'IMYAccount', :podspec => 'https://gitlab.meiyou.com/iOS/IMYAccount/raw/release-jingqi-8.83.0/IMYAccount.podspec'
  pod 'IMYAccount', :path => '../IMYAccount'
  # 动画
  pod 'lottie-ios', :podspec => 'https://gitlab.meiyou.com/Github-iOS/lottie-ios/raw/source/lottie-ios.podspec'
  
  # 反馈
#  pod 'IMYFeedback', :podspec => 'https://gitlab.meiyou.com/iOS/IMYFeedback/raw/release-8.60.0/IMYFeedback.podspec'
  pod 'IMYFeedback', :path => '../IMYFeedback'
  
  pod 'LookinServer', :configurations => ['Debug'] # 方便查看 CI 包的 UI 层级
  
  # DEBUG调试用
  pod 'IMYDEBUG', :podspec => 'https://gitlab.meiyou.com/iOS/IMYDEBUG/raw/master-library/IMYDEBUG.podspec', :configurations => ['Debug']
  
  pod 'libpag', :podspec => 'https://gitlab.meiyou.com/Github-iOS/libpag/raw/release-4.2.100/libpag.podspec'
  
  pod 'LKDBHelper', :podspec => 'https://gitlab.meiyou.com/Github-iOS/LKDBHelper/raw/master/LKDBHelper.podspec'
  pod 'FMDB', :podspec => 'https://gitlab.meiyou.com/Github-iOS/FMDB/raw/master/FMDB.podspec'
  pod 'YYModel', :podspec => 'https://gitlab.meiyou.com/Github-iOS/YYModel/raw/master/YYModel.podspec'
  pod 'YYImage', :podspec => 'https://gitlab.meiyou.com/Github-iOS/YYImage/raw/source/YYImage.podspec'
  pod 'YYCache', :podspec => 'https://gitlab.meiyou.com/Github-iOS/YYCache/raw/master/YYCache.podspec'
  pod 'YYText', :podspec => 'https://gitlab.meiyou.com/Github-iOS/YYText/raw/master/YYText.podspec'
  
end
