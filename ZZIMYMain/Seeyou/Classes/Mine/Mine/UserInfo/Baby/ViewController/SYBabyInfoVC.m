//
//  SYBabyInfoVC.m
//  Seeyou
//
//  Created by zlj on 2019/12/20.
//  Copyright © 2019 linggan. All rights reserved.
//

#import "SYBabyInfoVC.h"
#import "SYNicknameEditVC.h"
#import "NSDate+SY.h"
#import "SYGlobalMacros.h"
#import <IMYRecordBabyManager.h>
#import <IMYRecordTakeImageUtil.h>
#import <IMYLamaHome/IMYLHABTestManager.h>
#import "SYBabyListVC.h"
#import "SYDeleteTableViewCell.h"
#import "SYSwitchShowTableViewCell.h"
#import "IMYYQHomeABTestManager.h"
#import <IMYCommonKit/IMYCKABTestManager.h>
#import <IMYYunyuHome/IMYYunYuABManager.h>
#import <IMYYunyuHome/IMYRelativeBabySearchResultModel.h>
#import <IMYYunyuHome/IMYBabyDetailRelativeFriendsVC.h>
#import <BBJBabyHome/BBJBabyCacheManager.h>
#import <IMYTools/IMYBabyMVTemplateViewModel.h>
#import <BBJBabyHome/BBJPhotoPickerViewController.h>
#import <BBJBabyHome/BBJMeiYouPhotoLibrayViewController.h>
#import <BBJBabyHome/BBJBabyCoverPhotoNewFeatureAlert.h>
#import <BBJBabyHome/BBJCustomCameraVC.h>
#import <BBJBabyHome/BBJAlbumHelper.h>
@interface SYBabyInfoVC () <
    UITableViewDelegate,
    UITableViewDataSource,
    UINavigationControllerDelegate,
    UIImagePickerControllerDelegate,
    IMYAssetPickerControllerDelegate
>

@property (nonatomic, assign) BOOL isAvatarUpload;
@property (nonatomic, assign) BOOL isBabyBGUpload;
@property (nonatomic, strong) IMYRecordBabyModel *originBabyModel;
@property (nonatomic, assign) NSInteger babyFollowerCount;  //该宝宝被关注的亲友数
@property (nonatomic, strong) NSString *babyFollowerUri;
@property (nonatomic, assign) BOOL selectedBirthday;
@property (nonatomic, assign) BOOL hasLastRecord;
@property (nonatomic, strong) NSString *lastRecord;

@property (nonatomic, strong) IMYCKLoadingTextButton *deleteButton;//删除按钮
@property (nonatomic, strong) UIButton *rightclickButton_iOS12;
@property (nonatomic, strong) IMYPickerView *genderPicker;
@property (nonatomic, strong) IMYPickerView *birthdayPicker;
@property (nonatomic, assign) BOOL alter_baby; //是否可以编辑宝宝信息 (关注宝宝需要)


@property (nonatomic, assign) NSInteger updateImageviewType;//0 全部 1 头像 2 背景
@property (nonatomic, strong) IMYButton *changeBGButton;//更换背景按钮
@property (nonatomic, strong) UIView *changeBGButtonMaskView;//更换背景按钮mask
@property (strong, nonatomic) UIView *coverBgMaskView;
@property (nonatomic, strong) UIView *avatarBgView;//头像描边
@property (nonatomic, assign) NSInteger selectedPhotoTypeForBI;//埋点选择上传照片类型 相册/拍照

//首页显示
@property (nonatomic,strong)SYSwitchShowTableViewCellModel *switchMode;
@end

@implementation SYBabyInfoVC

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupBaseData];
    [self setupBaseSubviews];
    [self updateView];
    //获取亲友关系 /权限
    [self reqBBJBabyInfo];
    //获取宝宝最后一次记录
    [self reqBBJBabyLastRecord];
    //通知相关
    [self babyInfoChangedNotify];
}

- (void)setupBaseData {
    self.title = IMYString(@"宝宝信息");
    self.saveImmediately = YES;
    self.saveBabyBGImmediately = YES;
    
    self.updateImageviewType = 0;//默认头像/背景
    if (self.isAddBaby) {
        self.updateImageviewType = 1;//更新头像
    }
    self.babyFollowerCount = -1;
    
    self.originBabyModel = [[IMYRecordBabyModel alloc] init];
    self.originBabyModel.birthday = [IMYPublicAppHelper shareAppHelper].babyBirthday;
    self.originBabyModel.gender = [IMYPublicAppHelper shareAppHelper].baby_sex;
    self.originBabyModel.nickname = [IMYPublicAppHelper shareAppHelper].baby_nick;
    
    
    @weakify(self);
    NSMutableArray *dataSource = [NSMutableArray array];
    //宝宝昵称
    SYTextTableViewCellModel *nicknameModel = [[SYTextTableViewCellModel alloc] init];
    nicknameModel.cellType = SYBabyInfoNikname;
    nicknameModel.configureHandler = ^(SYTextTableViewCell *cell) {
        @strongify(self);
        cell.titleLabel.text = IMYString(@"宝宝昵称");
        cell.textField.text = imy_isNotBlankString(self.babyModel.nickname) ? self.babyModel.nickname : IMYString(@"宝宝");
    };
    @weakify(nicknameModel);
    nicknameModel.selectHandler = ^{
        @strongify(self);
        //添加宝宝中 configureHandler 会重写
        SYNicknameEditVC *vc = [[SYNicknameEditVC alloc] init];
        vc.babyModel = self.babyModel;
        vc.completion = ^(BOOL save, NSString *nickname) {
            @strongify(self, nicknameModel);
            if (save) {
                self.babyModel.nickname = nickname;
                self.hasSyncBabyInfo = YES;
                [self.tableView reloadData];
                nicknameModel.curText = nickname;
            }
            [self modelFinishSelect:nicknameModel];
        };
        [self imy_push:vc];
    };
    [dataSource addObject:nicknameModel];
    self.nicknameCellModel = nicknameModel;
    
    //宝宝性别
    SYTextTableViewCellModel *genderModel = [[SYTextTableViewCellModel alloc] init];
    genderModel.cellType = SYBabyInfoGender;
    genderModel.configureHandler = ^(SYTextTableViewCell *cell) {
        @strongify(self);
        cell.textField.userInteractionEnabled = NO;
        cell.titleLabel.text = IMYString(@"宝宝性别");
        cell.textField.text = [self.babyModel genderString];
    };
    @weakify(genderModel);
    genderModel.selectHandler = ^{
        @strongify(self);
        NSArray *data = @[IMYString(@"小公主"), IMYString(@"小王子")];
        NSString *gender = self.babyModel.gender == IMYRecordBabyGenderBoy ? data.lastObject : data.firstObject;
        IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
                                                                          dataArray:@[data]
                                                                      pickerViewTpe:IMYPickerViewTypeCustom
                                                                       confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self, genderModel);
            IMYRecordBabyGender gender = [result.resultString isEqualToString:IMYString(@"小公主")] ? IMYRecordBabyGenderGirl : IMYRecordBabyGenderBoy;
            if (![IMYNetState networkEnable] && !self.isAddBaby) {
                [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
            }else{
                IMYRecordBabyGender gender_old = self.babyModel.gender;
                if (gender != self.babyModel.gender) {
                    self.babyModel.gender = gender;
                    self.hasSyncBabyInfo = YES;
                    [self.tableView reloadData];
                    //孕育身份修改性别接口
                    if (self.babyModel.is_owner) {
                        [self modelFinishSelect:genderModel];
                        
                        if (!self.isAddBaby) {
                            [UIWindow imy_showLoadingHUDWithText:IMYString(@"保存中")];
                            @weakify(self);
                            [self syncBabyInfoToServerWithPosition:18 completion:^(BOOL success, NSString *errorMessage) {
                                @strongify(self);
                                [UIWindow imy_hideHUD];
                                if (success) {
                                    [UIWindow imy_showTextHUDWithDelay:2 image:[UIImage imy_imageForKey:@"toast_icon_yes"] text:IMYString(@"保存成功")];
                                }else{
                                    self.babyModel.gender = gender_old;
                                    self.hasSyncBabyInfo = YES;
                                    [self.tableView reloadData];
                                }
                            }];
                        }
                    } else {
                        //关注宝宝身份修改性别接口--bbj
                        BBJ_POST_NOTIFY(BBJNotification_updateBabySex, nil);
                        if (!self.isAddBaby) {//非添加页面采取请求接口
                            NSMutableDictionary *param = [[NSMutableDictionary alloc] initWithDictionary:@{@"sex": @(gender)}];
                            [self updateBabyInfoWithParamter:param babyId:self.babyModel.bbj_baby_id completion:^(id  _Nullable resData, NSError * _Nullable error) {
                                imy_asyncMainBlock(^{
                                    @strongify(self);
                                    [UIWindow imy_hideHUD];
                                    if (error) {
                                        self.babyModel.gender = gender_old;
                                        if (![IMYNetState networkEnable]) {
                                            [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                                        } else {
                                            [UIWindow imy_showTextHUD:IMYString(@"加载失败，请重试")];
                                        }
                                        return;
                                    }
                                    self.babyModel.gender = gender;
                                    [UIWindow imy_showTextHUDWithDelay:2 image:[UIImage imy_imageForKey:@"toast_icon_yes"] text:IMYString(@"保存成功")];
                                    BBJ_POST_NOTIFY(BBJNotification_updateBabySex, nil);
                                });
                            }];
                        }
                    }
                }
                [self.genderPicker hide];
            }
        } cancelBlock:nil];
        picker.title = IMYString(@"选择宝宝性别");
        picker.autoReleaseSelf = YES;
        if (!self.isAddBaby) {
            picker.outDismissControl = YES;
        }
        [picker setSelectWithString:gender];
        [picker show];
        self.genderPicker = picker;
    };
    [dataSource addObject:genderModel];
    self.genderCellModel = genderModel;
    
    //宝宝出生日
    SYTextTableViewCellModel *birthdayModel = [[SYTextTableViewCellModel alloc] init];
    birthdayModel.cellType = SYBabyInfoBirthday;
    birthdayModel.configureHandler = ^(SYTextTableViewCell *cell) {
        @strongify(self);
        cell.textField.userInteractionEnabled = NO;
        cell.titleLabel.text = IMYString(@"宝宝出生日");
        if (self.babyModel.birthday.length) {
            cell.textField.text = [[self.babyModel.birthday imy_getOnlyDate] getCNDateString];
        } else {
            cell.textField.text = IMYString(@"未选择");
        }
    };
    @weakify(birthdayModel);
    birthdayModel.selectHandler = ^{
        @strongify(self);
        NSDate *minDate;
        if (self.minBirthday) {
            minDate = self.minBirthday;
        } else {
            minDate = [@"2000-1-1" imy_getOnlyDate];
        }
        NSArray *data = @[minDate, [NSDate imy_today]];
        NSDate *date = self.babyModel.birthday.length ? [self.babyModel.birthday imy_getOnlyDate] : [NSDate imy_today];
        IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
                                                                          dataArray:data
                                                                      pickerViewTpe:IMYPickerViewTypeDate
                                                                       confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self, birthdayModel);
            if (self.babyModel.is_owner) {
                //孕育身份修改生日接口
                NSString *birthdayString = [result.resultDate imy_getOnlyDateString];
                if (![IMYNetState networkEnable] && !self.isAddBaby) {
                    [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                }else{
                    NSString *birthdayString_old = self.babyModel.birthday;
                    if (![birthdayString isEqualToString:self.babyModel.birthday]) {
                        self.babyModel.birthday = birthdayString;
                        self.hasSyncBabyInfo = YES;
                        [self.tableView reloadData];
                        birthdayModel.curText = birthdayString;
                        [self modelFinishSelect:birthdayModel];
                        if (!self.isAddBaby) {
                            [UIWindow imy_showLoadingHUDWithText:IMYString(@"保存中")];
                            @weakify(self);
                            [self syncBabyInfoToServerWithPosition:18 completion:^(BOOL success, NSString *errorMessage) {
                                @strongify(self);
                                [UIWindow imy_hideHUD];
                                if (success) {
                                    [UIWindow imy_showTextHUDWithDelay:2 image:[UIImage imy_imageForKey:@"toast_icon_yes"] text:IMYString(@"保存成功")];
                                }else{
                                    self.babyModel.birthday = birthdayString_old;
                                    self.hasSyncBabyInfo = YES;
                                    [self.tableView reloadData];
                                }
                            }];
                        }
                    }
                    [self.birthdayPicker hide];
                }
            } else { //关注宝宝身份修改生日接口--bbj
                NSString *birthdayString_three = [result.resultDate imy_getOnlyDateString];//添加宝宝接口 需要 yyyy-MM-dd
                NSString *birthdayString = [result.resultDate imy_getDateTimeString];//上传生日接口需要  yyyy-MM-dd HH:mm:ss
                if (![IMYNetState networkEnable] && !self.isAddBaby) {
                    [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                }else{
                    NSString *birthdayString_old = self.babyModel.birthday;
                    
                    if (![birthdayString_three isEqualToString:self.babyModel.birthday]) {
                        self.babyModel.birthday = birthdayString_three;
                        self.hasSyncBabyInfo = YES;
                        birthdayModel.curText = birthdayString;
                        [self.tableView reloadData];
                        if (!self.isAddBaby) {
                            //生日请求接口 884更换bbj一致
                            @weakify(self);
                            NSMutableDictionary *param = [[NSMutableDictionary alloc] initWithDictionary:@{@"birthday": birthdayString}];
                            [self updateBabyInfoWithParamter:param babyId:self.babyModel.bbj_baby_id completion:^(id  _Nullable resData, NSError * _Nullable error) {
                                imy_asyncMainBlock(^{
                                    @strongify(self);
                                    [UIWindow imy_hideHUD];
                                    
                                    if (error) {
                                        self.babyModel.birthday = birthdayString_old;
                                        self.hasSyncBabyInfo = YES;
                                        [self.tableView reloadData];
                                        if (![IMYNetState networkEnable]) {
                                            [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                                        } else {
                                            [UIWindow imy_showTextHUD:IMYString(@"加载失败，请重试")];
                                        }
                                        return;
                                    }
                                    [UIWindow imy_showTextHUDWithDelay:2 image:[UIImage imy_imageForKey:@"toast_icon_yes"] text:IMYString(@"保存成功")];
                                    NSMutableDictionary *birthdayDic = [[NSMutableDictionary alloc] init];
                                    [birthdayDic imy_setNonNilObject:@(self.babyModel.bbj_baby_id) forKey:@"baby_id"];
                                    [birthdayDic imy_setNonNilObject:birthdayString forKey:@"birthday"];
                                    BBJ_POST_NOTIFY(BBJNotification_updateBabyBirthdayOrDue, birthdayDic);
                                });
                            }];
                        }
                    }
                    [self.birthdayPicker hide];
                }
            }
        } cancelBlock:nil];
        picker.title = IMYString(@"选择宝宝出生日");
        picker.autoReleaseSelf = YES;
        if (!self.isAddBaby) {
            picker.outDismissControl = YES;
        }
        [picker setSelectWithDate:date];
        [picker show];
        self.birthdayPicker = picker;
    };
    [dataSource addObject:birthdayModel];
    self.birthdayCellModel = birthdayModel;
    
    //我是宝宝的
    if (!self.isAddBaby) {//(孕育/关注)宝宝都有 关系行cell
        SYTextTableViewCellModel *relativeModel = [[SYTextTableViewCellModel alloc] init];
        relativeModel.cellType = SYBabyInfoRelative;
        relativeModel.configureHandler = ^(SYTextTableViewCell *cell) {
            @strongify(self);
            cell.titleLabel.text = IMYString(@"我是宝宝的");
            NSString * relationName;
            if (imy_isNotBlankString(self.babyModel.relation_name)) {
                relationName = self.babyModel.relation_name;
            } else {
                relationName = IMYString(@"其他");
            }
            cell.textField.text = relationName;
        };
        @weakify(relativeModel);
        relativeModel.selectHandler = ^{
            @strongify(self);  //添加宝宝中 configureHandler 会重写
            [IMYEventHelper bbjEvent:@"bbqjl_wsbbd"];
            BBJBabyListModel *bbjBabyListModel = [IMYRecordBabyModel convetIMYBabyModeToBBJRecordBaby:self.babyModel];
      
            IMYRelativeBabySearchResultModel *resultModel = [IMYRelativeBabySearchResultModel getRelativeBabySearchResultModelfrom:bbjBabyListModel];
            IMYBabyDetailRelativeFriendsVC *vc = [[IMYBabyDetailRelativeFriendsVC alloc] initWithResultModel:resultModel];
            vc.updateRelationAction = ^(BBJRelationModel * _Nonnull model) {
                @strongify(self, relativeModel);
                [self handleChangeRelation:model CellModel:relativeModel];
            };
            IMYPublicBaseNavigationController *nav = [[IMYPublicBaseNavigationController alloc] initWithRootViewController:vc];
            [self presentViewController:nav animated:YES completion:nil];

        };
        [dataSource addObject:relativeModel];
        self.relativeModel = relativeModel;
    }
    
    self.dataSource = dataSource;
    
    //构建展示宝宝 cellmodel
    self.switchMode = [SYSwitchShowTableViewCellModel new];
    self.switchMode.title = @"首页展示";
    //是否需要展示"宝宝首页展示" cell的 选择开关
    if (self.babyModel) {
        self.switchMode.isOn = self.babyModel.homepage_display;
    }
}
//关注宝宝请求接口
- (void)updateBabyInfoWithParamter:(NSDictionary *)dict babyId:(NSInteger)babyId completion:(BBJCompletionBlk)completion {
    @weakify(self);
    NSMutableDictionary *param = [[NSMutableDictionary alloc] initWithDictionary:dict];
    [param imy_setNonNilObject:@(babyId) forKey:@"baby_id"];
    
    [BBJServerRequest putPath:@"baby?api_version=1" params:[param copy] completion:^(id  _Nullable data, NSError * _Nullable error) {
        if (completion) {
            completion(data,nil);
        }
    }];
}
- (void)handleChangeRelation:(BBJRelationModel *)relation CellModel:(SYTextTableViewCellModel *)cellModel{
    if ([self.babyModel.relation_name isEqualToString:relation.relation_name]
        && self.babyModel.relation == relation.relation) {
        return ;
    }
    
    NSString *userId = [IMYPublicAppHelper shareAppHelper].userid;
    NSMutableDictionary *dict = [[NSMutableDictionary alloc] init];
    [dict imy_setNonNilObject:@(relation.relation) forKey:@"relation"];
    [dict imy_setNonNilObject:relation.relation_name forKey:@"relation_name"];
    [dict imy_setNonNilObject:userId forKey:@"user_id"];
    [dict imy_setNonNilObject:@(self.babyModel.bbj_baby_id) forKey:@"baby_id"];
    
    @weakify(self);
    [BBJServerRequest putPath:@"follow/info" params:[dict copy] completion:^(id  _Nullable data, NSError * _Nullable error) {
        @strongify(self);
        imy_asyncMainBlock(^{
            if (error) {
                if ([IMYNetState networkEnable]) {
                    [self bbj_showErrorMessage:error];
                } else {
                    [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                }
                return ;
            }else {
                [UIWindow imy_showTextHUDWithDelay:2 image:[UIImage imy_imageForKey:@"toast_icon_yes"] text:IMYString(@"保存成功")];
                self.babyModel.relation_name = relation.relation_name;
                self.babyModel.relation= relation.relation;
                self.relativeModel.curText = relation.relation_name;
                [self.tableView reloadData];
                [self modelFinishSelect:cellModel];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    //关注宝宝的修改权限(更改关系后,需要再次请求)
                    [self reqBBJBabyInfo];
                });
            }
        });
    }];
}

- (void)setupBaseSubviews {
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.estimatedRowHeight = 0;
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    self.tableView.tableFooterView = [UIView new];
    [self.tableView registerClass:[SYTextTableViewCell class] forCellReuseIdentifier:@"SYTextTableViewCell"];
    [self.tableView registerClass:[SYDeleteTableViewCell class] forCellReuseIdentifier:@"SYDeleteTableViewCell"];
    [self.tableView registerClass:[SYSwitchShowTableViewCell class] forCellReuseIdentifier:@"SYSwitchShowTableViewCell"];

    [self.view addSubview:self.tableView];
    
    self.headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 200)];
    UIView *cardView = [[UIView alloc] initWithFrame:CGRectMake(12, 8, SCREEN_WIDTH - 24, 192)];
    [cardView imy_setBackgroundColor:kCK_White_AN];
    [self.headerView addSubview:cardView];
    self.headerView.backgroundColor = [UIColor clearColor];
    
    if (!self.isAddBaby) {
        //背景图
        self.babyBGImageView = [[UIImageView alloc] init];
        self.babyBGImageView.contentMode = UIViewContentModeScaleAspectFill;
        self.babyBGImageView.clipsToBounds = YES;
        [self.babyBGImageView imy_setBackgroundColor:kCK_White_AN];
        self.babyBGImageView.layer.cornerRadius = 12;
        self.babyBGImageView.userInteractionEnabled = YES;
        [cardView addSubview:self.babyBGImageView];
        //换背景按钮
        self.changeBGButtonMaskView = [UIView new];
        self.changeBGButtonMaskView.alpha = 0.4;
        [self.changeBGButtonMaskView imy_drawAllCornerRadius:12];
        [self.changeBGButtonMaskView setBackgroundColor:[UIColor colorWithHexString:@"#000000"]];
        [cardView addSubview:self.changeBGButtonMaskView];
        [cardView addSubview:self.changeBGButton];
        //换背景按钮 埋点
        [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_ghbj_hbj",
                                                          @"action": @(1)
                                                       } headers:nil completed:nil];
        self.coverBgMaskView = [UIView new];
        self.coverBgMaskView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.15];
        [self.babyBGImageView addSubview:self.coverBgMaskView];
    }
    
    self.tableView.tableHeaderView = self.headerView;
    [cardView imy_drawAllCornerRadius:12];
    self.avatarImageView = [[IMYAvatarImageView alloc] init];
    self.avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.avatarImageView.imy_showViewSize = self.avatarImageView.imy_size;
    self.avatarImageView.clipsToBounds = YES;
    [self.avatarImageView imy_setBackgroundColor:kCK_Black_FN];
    self.avatarImageView.layer.cornerRadius = 36;
    self.avatarImageView.userInteractionEnabled = YES;
    @weakify(self);
    [self.avatarImageView bk_whenTapped:^{
        @strongify(self);
        [self iconAction:YES];
    }];
    [cardView addSubview:self.avatarBgView];
    [self.avatarBgView addSubview:self.avatarImageView];
    
    self.iconCameraFlag = [[UIImageView alloc] init];
    [self.iconCameraFlag imy_setImage:@"ertai_pic_photo2"];
    [cardView addSubview:self.iconCameraFlag];
    
    [self setupBaseConstraintsWithSuperView:cardView];
}
- (void)setupBaseConstraintsWithSuperView:(UIView *)superView {
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    if (!self.isAddBaby) {
        [self.babyBGImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
        [self.coverBgMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
        
        [self.avatarBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(72+3, 72+ 3));
            make.centerY.mas_equalTo(superView);
            make.left.equalTo(superView).offset(24);
        }];
        
        [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(self.avatarBgView);
            make.width.height.mas_equalTo(72);
        }];
        
        [self.iconCameraFlag mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(23, 23));
            make.right.equalTo(self.avatarImageView.mas_right).offset(0);
            make.bottom.equalTo(self.avatarImageView.mas_bottom).offset(0);
        }];
        
        [self.changeBGButtonMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(71, 24));
            make.top.equalTo(superView).mas_offset(12);
            make.right.equalTo(superView.mas_right).mas_offset(-12);
        }];
        [self.changeBGButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(self.changeBGButtonMaskView);
        }];
    } else {
        [self.avatarBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(72+3, 72+ 3));
            make.center.mas_equalTo(CGSizeZero);
        }];
        
        [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(self.avatarBgView);
            make.width.height.mas_equalTo(72);
        }];
        
        [self.iconCameraFlag mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(23, 23));
            make.right.equalTo(self.avatarImageView.mas_right).offset(0);
            make.bottom.equalTo(self.avatarImageView.mas_bottom).offset(0);
        }];
    }
}
//头像描边背景
- (UIView *)avatarBgView{
    if (!_avatarBgView) {
        CGFloat height_width = 72 + 3;
        UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, height_width, height_width)];
        [view imy_setBackgroundColor:[[UIColor imy_colorForKey:kCK_White_A] colorWithAlphaComponent:0.5]];
        [view imy_drawAllCornerRadius:(72 + 3)/2];
        _avatarBgView = view;
    }
    return _avatarBgView;
}

- (IMYButton *)changeBGButton {
    if (!_changeBGButton) {
        _changeBGButton = [IMYButton buttonWithType:UIButtonTypeCustom];
        [_changeBGButton addTarget:self action:@selector(changeBGAction:) forControlEvents:UIControlEventTouchUpInside];
        [_changeBGButton imy_drawAllCornerRadius:12];
        
        [_changeBGButton imy_setBackgroundColor:kCK_Clear_A];
        
        _changeBGButton.offset = 2;
        _changeBGButton.imageAtDirection = IMYDirectionLeft;
        _changeBGButton.titleAtDirection = IMYDirectionRight;
        
        
        _changeBGButton.titleLabel.font = [UIFont imy_FontWith:11.0f];
        [_changeBGButton imy_setTitleColor:kCK_White_A];
        [_changeBGButton imy_setTitle:IMYString(@"换背景")];
        [_changeBGButton imy_setImage:@"nxjk_detail_information_icon_background"];
    }
    return _changeBGButton;
}
//换背景点击
- (void)changeBGAction:(UIButton *)render {
    //只更新背景
    [self iconAction:NO];
    [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_ghbj_hbj",
                                                      @"action": @(2)
                                                   } headers:nil completed:nil];
}

- (void)handleSaveAction {
    if (self.hasSyncBabyInfo) {
        [self.rightNavButton showLoading:YES];
        [UIWindow imy_showTextHUDWithoutUI];
        @weakify(self);
        [self syncBabyInfoToServerWithPosition:18 completion :^(BOOL success, NSString *errorMessage) {
            @strongify(self);
            [self.rightNavButton showLoading:NO];
            [UIWindow imy_hideHUD];
            if (success) {
                [self imy_topLeftButtonTouchupInside] ;
            } else {
                
            }
        }];
    } else {
        [self imy_topLeftButtonTouchupInside] ;
    }
}

- (void)babyInfoChangedNotify {
    @weakify(self);
    //当删除宝宝的时候 。及时刷新 宝宝亲友数量。
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"BBJNotification_removeRelation" object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *notify) {
        @strongify(self);
        NSInteger newFollowers = [notify.object integerValue];
        if (newFollowers <= 2) {
            [self reqBBJBabyInfo];
        }
    }];
}

- (void)syncBabyInfoToServerWithPosition:(NSInteger)position completion:(void (^)(BOOL success,NSString *errorMessage))completion  {
    [IMYRecordBabyManager syncBabyInfoToServerWithBabyModel:@[self.babyModel]
                                                   position:position
                                                      scene:nil
                                              andCompletion:^(BOOL success, NSString *errorMessage) {
        if (completion) {
            completion(success, errorMessage);
        }
        if (!success && position != 12) {
            imy_asyncMainBlock(^{
                if (![IMYNetState networkEnable]) {
                    [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                } else {
                    [UIWindow imy_showTextHUD:IMYString(@"加载失败，请重试")];
                }
            });
        }
    }];
}
//背景修改后同步服务器
- (void)syncBabyCoverToServerWithUrlString:(NSString *)url Completion:(void (^)(BOOL success,NSString *errorMessage))completion {
    NSMutableDictionary *dict = [NSMutableDictionary new];
    if (url != nil) {
        [dict setObject:url forKey:@"cover"];
    }
    [dict imy_setNonNilObject:@(self.babyModel.bbj_baby_id) forKey:@"baby_id"];
    
    @weakify(self);
    [[IMYServerRequest putPath:@"baby" host:api_bbj_meiyou_com params:dict headers:nil] subscribeNext:^(id x) {
        
        if (completion) {
            completion(YES, nil);
        }
        imy_asyncMainBlock(0.2, ^{
            @strongify(self);
            //宝宝背景上传成功 更新首页背景图
            [BBJBabyCacheManager updateBabyCover:url commonBabyId:self.babyModel.baby_id];
        });
    } error:^(NSError *error) {
        if (completion) {
            completion(NO, nil);
        }
    }];
}
//关注宝宝是否显示到首页接口
- (void)syncBabyDisplayToHomepageWithBabyID:(NSInteger )BabyID  isDisplay:(NSInteger)isDisplay Completion:(void (^)(BOOL success,NSString *errorMessage))completion {

      NSDictionary *params = @{
          @"baby_id": @(BabyID),
          @"homepage_display": @(isDisplay)
      };
      
      
      [UIView imy_showLoadingHUD];
      [[[IMYServerRequest postPath:@"v3/follow/extend"
                              host:api_bbj_meiyou_com
                            params:params
                           headers:nil] deliverOnMainThread]
       subscribeNext:^(id  _Nullable x) {
          if (completion) {
              completion(YES, nil);
          }
        
      } error:^(NSError * _Nullable error) {
          if (error) {
              imy_asyncMainBlock(^{
                  if (![IMYNetState networkEnable]) {
                      [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                  } else {
                      [UIWindow imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
                  }
              });
          }
      }];
}
#pragma mark - Private
- (void)updateView {
    //0 全部 1 头像 2 背景
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        if (self.updateImageviewType == 0) {
            [self updateBabyAvatarImageView];
            [self updateBabyCoverImageView];
        } else if (self.updateImageviewType == 1) {
            [self updateBabyAvatarImageView];
        }else if (self.updateImageviewType == 2){
            [self updateBabyCoverImageView];
        }
    });
}
//更新头像ImageView
- (void)updateBabyAvatarImageView{
    if (self.avatar) {
        [self.avatarImageView imy_setImage:self.avatar];
    } else {
        if (self.isAddBaby == NO) {
            self.avatarImageView.imy_showViewSize = self.avatarImageView.imy_size;
            NSString *avatarString = [BBJImageCache markUpFollowerAvatar:self.babyModel.avatar];
            [self.avatarImageView setAvatarWithURLString:avatarString placeholder:[self.babyModel genderPlaceholdImage]];
        }
    }
}
//更新背景ImageView
- (void)updateBabyCoverImageView{
    if (self.cover) {
        [self.babyBGImageView imy_setImage:self.cover];
    } else {
        self.babyBGImageView.imy_showViewSize = self.babyBGImageView.imy_size;
        NSInteger common_baby_id = self.babyModel.baby_id;

        NSString *cover = [BBJBabyCacheManager getBabyCoverWithCommonBabyId:common_baby_id];
        if (imy_isNotBlankString(cover) && cover.length > 0) {
            CGSize viewSize = self.babyBGImageView.imy_showViewSize;
            BOOL webp = self.babyBGImageView.imy_showWebP;
            IMY_QiNiu_ImageType type = webp ? IMY_QiNiu_WEBP : IMY_QiNiu_None;
            CGSize cdnSize = CGSizeZero;
            if (viewSize.width > 0 && viewSize.height > 0) {
                cdnSize = CGSizeMake(ceil(viewSize.width * IMYSystem.screenScale), ceil(viewSize.height * IMYSystem.screenScale));
            }
            NSString *cacheKey = [NSString qiniuURL:cover resize:cdnSize mode:0 quality:0 type:type];
            UIImage *img = [[SDImageCache sharedImageCache] imageFromMemoryCacheForKey:cacheKey];
            if (!img) {
                img = [[SDImageCache sharedImageCache] imageFromDiskCacheForKey:cacheKey];
            }
            if (img) {
                [self.babyBGImageView imy_setImage:img];
            }else{
                [self downloadImageCacheUrl:cacheKey coverUrl:cover retryCount:0];;
            }
        }else{
            [self setbabyBGImageViewDefaultImage];
        }
    }
}
- (void)setbabyBGImageViewDefaultImage {
    //可支持聚合后台配置默认背景图，仅当用户未设置背景图时可替换，若用户已更改背景图，需优先展示用户更改的背景图 8.88
      NSString *default_background_image = [[IMYConfigsCenter sharedInstance] stringForKeyPath:@"womens_health2.bbj_baby_home_config.default_background_image"];
      if (imy_isNotBlankString(default_background_image) && default_background_image.length > 0) {
          [self.babyBGImageView imy_setImageURL:default_background_image];
      }else {
          [self.babyBGImageView imy_setImage:@"bbj_baby_banner_img2"];
      }
}

- (void)downloadImageCacheUrl:(NSString *)cacheUrl coverUrl:(NSString *)coverUrl retryCount:(NSInteger)retryCount{
    ///最大重试两次
    if (retryCount >= 2) {
        [self.babyBGImageView imy_setImageURL:coverUrl];
        return;
    }
    NSURL *url = [NSURL URLWithString:cacheUrl];
    if (retryCount == 1) {
        url = [NSURL URLWithString:coverUrl];
    }
    @weakify(self);
    [[SDWebImageDownloader sharedDownloader] downloadImageWithURL:url
                                                          options:SDWebImageDownloaderHighPriority | SDWebImageDownloaderContinueInBackground |SDWebImageDownloaderAdvertisingPriority
                                                         progress:nil
                                                        completed:^(UIImage *image, NSData *data, NSError *error, BOOL finished) {
                                                           @strongify(self);
                                                            if (image && finished) {
                                                                [[SDImageCache sharedImageCache] storeImage:image recalculateFromImage:NO imageData:data forKey:cacheUrl toDisk:YES];
                                                                imy_asyncMainBlock(^{
                                                                    [self.babyBGImageView imy_setImage:image];
                                                                });
                                                            } else {
                                                                //下载失败 继续重试
                                                                [self downloadImageCacheUrl:cacheUrl coverUrl:coverUrl retryCount:retryCount + 1];
                                                            }
                                                        }];
}

- (void)uploadAvatar:(void (^)(BOOL success, NSString *url, NSString *errorMessage))completion {
    if (!self.avatar || self.isAvatarUpload) {
        completion(YES, nil, nil);
        return;
    }
    if (![IMYNetState networkEnable]) {
        completion(NO, nil, MT_Request_NoNetToast);
        return;
    }
    @weakify(self);
    NSData *imageData = UIImageJPEGRepresentation(self.avatar, 0.9);
    NSString *fileName = [NSString stringWithFormat:@"%@-%@-%@_%ld_%ld.jpg", @"baby-avatar", [IMYPublicAppHelper shareAppHelper].userid ?: @"0", [NSUUID UUID].UUIDString, (long)self.avatar.size.width, (long)self.avatar.size.height];
    id<IMYOSSFileObject> obj = [[IMYOSS defaultUploader] fileObjectWithName:fileName data:imageData];
    obj.uploadScene = IMY_OSS_UPLOAD_SCENE_BBTX;
    [[IMYOSS defaultUploader] uploadObject:obj
                             progressBlock:nil
                            complatedBlock:^(id<IMYOSSFileObject>  _Nonnull object, NSError * _Nullable error) {
        @strongify(self);
        if (!error) {
            self.isAvatarUpload = YES;
            completion(YES, object.url.absoluteString, nil);
        } else {
            completion(NO, nil, IMYString(@"上传失败，请再试一次"));
        }
    }];
}

- (void)uploadBabyBG:(void (^)(BOOL success, NSString *url, NSString *errorMessage))completion {
    if (!self.cover || self.isBabyBGUpload) {
        completion(YES, nil, nil);
        return;
    }
    if (![IMYNetState networkEnable]) {
        completion(NO, nil, MT_Request_NoNetToast);
        return;
    }
    @weakify(self);
    NSData *imageData = UIImageJPEGRepresentation(self.cover, 0.9);
    NSString *fileName = [NSString stringWithFormat:@"%@-%@-%@_%ld_%ld.jpg", @"baby-cover", [IMYPublicAppHelper shareAppHelper].userid ?: @"0", [NSUUID UUID].UUIDString, (long)self.cover.size.width, (long)self.cover.size.height];
    id<IMYOSSFileObject> obj = [[IMYOSS defaultUploader] fileObjectWithName:fileName data:imageData];
    obj.uploadScene = IMY_OSS_UPLOAD_SCENE_BBHSYBJT;
    [[IMYOSS defaultUploader] uploadObject:obj
                             progressBlock:nil
                            complatedBlock:^(id<IMYOSSFileObject>  _Nonnull object, NSError * _Nullable error) {
        @strongify(self);
        if (!error) {
            self.isBabyBGUpload = YES;
            completion(YES, object.url.absoluteString, nil);
        } else {
            completion(NO, nil, IMYString(@"上传失败，请再试一次"));
        }
    }];
}

- (void)modelFinishSelect:(SYTextTableViewCellModel *)model {
    if (model == self.nicknameCellModel) {
        NSMutableDictionary *param = [[NSMutableDictionary alloc] init];
        [param imy_setNonNilObject:model.curText forKey:@"nickname"];
        [param imy_setNonNilObject:@(self.babyModel.bbj_baby_id) forKey:@"babyId"];
        if (!self.isAddBaby) {
            BBJ_POST_NOTIFY(BBJNotification_updateBabyNickname, param);
        }
    }  else if (model == self.birthdayCellModel) {
        NSMutableDictionary *param = [[NSMutableDictionary alloc] init];
        [param imy_setNonNilObject:model.curText forKey:@"birthday"];
        [param imy_setNonNilObject:@(self.babyModel.bbj_baby_id) forKey:@"babyId"];
        if (!self.isAddBaby) {
            BBJ_POST_NOTIFY(BBJNotification_updateBabyBirthdayOrDue, param);
        }
    }else if (model == self.relativeModel){
        NSMutableDictionary *param = [[NSMutableDictionary alloc] init];
        [param imy_setNonNilObject:@(self.babyModel.relation) forKey:@"relation"];
        [param imy_setNonNilObject:self.babyModel.relation_name forKey:@"relation_name"];
        [param imy_setNonNilObject:@(self.babyModel.bbj_baby_id) forKey:@"baby_id"];
        if (!self.isAddBaby) {
            BBJ_POST_NOTIFY(@"BBJNotification_updateRelationWithBBJUserSecondModelBaby", param);
            BBJ_POST_NOTIFY(BBJNotification_updateRelationWithBaby, param);
        }
    }
}

#pragma mark - Action
- (void)deleteAction {
    
    BOOL lamaHomeBBJEntrance = [IMYLHABTestManager lamaHomeBBJEntrance];
    
    if ([IMYCKABTestManager isYunyuHomeContainStyle] &&
        [IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeLama) {
        if ([[IMYRecordBabyManager sharedInstance] babyList].count == 1) {
            [UIWindow imy_showTextHUD:IMYString(@"需保留1个宝宝哦")];
            return;
        }
    } else if (![IMYYQHomeABTestManager isShowNewBabyList_783]) {
        if (lamaHomeBBJEntrance && [[IMYRecordBabyManager sharedInstance] babyList].count == 1) {
            [UIWindow imy_showTextHUD:IMYString(@"需保留1个宝宝哦")];
            return;
        }
    }
    if (self.babyFollowerCount == -1 /* || ![IMYNetState networkEnable] */) {
        [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
        return;
    }
    
    NSString *titleStr = nil;
    NSString *messageStr = nil;
    NSString *rightBtnTitle = IMYString(@"删除");
    NSString *cancelButtonTitle = IMYString(@"取消");
    NSTextAlignment textAlignment = NSTextAlignmentCenter;
    if (self.babyFollowerCount > 0 && self.babyFollowerUri.imy_charCount > 0) {  //存在亲友的时候 优先级最高
        titleStr = IMYString(@"删除宝宝提醒");
        messageStr = [NSString stringWithFormat:IMYString(@"您的%@，还有%ld个亲友关系未解除。需解除亲友关系，才可以删除宝宝。"), self.babyModel.nickname?:IMYString(@"宝宝"), self.babyFollowerCount];
        rightBtnTitle = IMYString(@"去解除");
    } else {
        ///该宝宝有最后一次记录，给出对应的删除提醒
        if (self.hasLastRecord && ![NSString imy_isBlankString:self.lastRecord]) {
            titleStr = IMYString(@"宝宝信息提醒");
            cancelButtonTitle = IMYString(@"暂不删除");
            rightBtnTitle = IMYString(@"继续删除");
            messageStr = [NSString stringWithFormat:IMYString(@"当前宝宝存在相册等宝宝记录数据，若删除该宝宝，其宝宝记录也将被删除\n%@"), self.lastRecord];
            textAlignment = NSTextAlignmentLeft;
        }else{
            messageStr = IMYString(@"要删除该宝宝吗？删除后该宝宝相关记录不可恢复");
        }
    }

    @weakify(self);
    [IMYActionMessageBox showBoxWithTitle:titleStr
                                  message:messageStr
                                    style:IMYMessageBoxStyleFlat
                        isShowCloseButton:NO
                            textAlignment:textAlignment
                        cancelButtonTitle:cancelButtonTitle
                         otherButtonTitle:rightBtnTitle
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        @strongify(self);
        if (sender == messageBox.rightButton) {
            /// 提示网络连接问题
            if (self.babyFollowerCount == -1 || ![IMYNetState networkEnable]) {
                [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                [messageBox dismiss];
                return;
            }
            
            //删除美柚宝宝前，需要解除宝宝记中对应的宝宝的亲友关系
            if (self.babyFollowerCount > 0 && self.babyFollowerUri.imy_charCount > 0) {
                /// 宝宝记中删除该宝宝，回调给美柚处理
                @weakify(self);
                BKBlock block = ^(){
                    @strongify(self);
                    [self bbjDeleteBabyActionDo];
                };
                
                IMYURI *uri = [IMYURI uriWithURIString:self.babyFollowerUri];
                [uri appendingParams:@{@"callback":block}];
                [[IMYURIManager shareURIManager] runActionWithURI:uri];
                [messageBox dismiss];
            } else {
                [self bbjDeleteBabyActionDo];
                [messageBox dismiss];
            }
        } else if (sender == messageBox.leftButton) {
            [messageBox dismiss];
        }
    }];
}

- (void)bbjDeleteBabyActionDo {
    [self sync_bbjDeleteBabyActionDo];
}
/// 同步，857新方案
- (void)sync_bbjDeleteBabyActionDo {
    // 进入宝宝记融合实验，在删除最小(选中)宝宝前，把选中宝宝设置成年龄第二小的宝宝
    if (self.babyModel.is_checked) {
        self.babyModel.is_deleted = YES;
        self.hasSyncBabyInfo = NO;
        NSMutableArray *serverArray = [[NSMutableArray alloc] init];
        [serverArray addObject:self.babyModel];
        
        IMYRecordBabyModel *preSelectModel = [[IMYRecordBabyManager sharedInstance] currentBaby];
        IMYRecordBabyModel *model = [[IMYRecordBabyManager sharedInstance] lastButOneBirthdayBaby];  // 第二小宝宝
        IMYRecordBabyModel *lastModel = [[IMYRecordBabyManager sharedInstance] lastBirthdayBaby];  // 年龄最小的宝宝
        // 怀孕备孕身份下，删除当前选中的宝宝后，不再去选中最小宝宝
        if ([IMYCKABTestManager isYunyuHomeContainStyle] &&
            [IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModeLama) {
            if (preSelectModel.baby_id != self.babyModel.baby_id) {//
                preSelectModel.is_checked = NO;
                [serverArray addObject:preSelectModel];
            }
        } else {
            if (preSelectModel.baby_id == lastModel.baby_id) {
                // 当删除的是最小宝宝的时候
                model.is_checked = YES;
                [serverArray addObject:model];
            } else {
                lastModel.is_checked = YES;
                [serverArray addObject:lastModel];
            }
        }
        [self.deleteButton showLoading:YES];
        self.deleteButton.userInteractionEnabled = YES;
        [UIWindow imy_showTextHUDWithoutUI];
        @weakify(self);
        if (self.babyModel.is_owner) {//孕育身份删除宝宝
            [IMYRecordBabyManager syncBabyInfoToServerWithBabyModel:serverArray
                                                           position:191
                                                              scene:nil
                                                      andCompletion:^(BOOL success, NSString *errorMessage) {
                @strongify(self);
                [self.deleteButton showLoading:NO];
                self.deleteButton.userInteractionEnabled = NO;
                [UIWindow imy_hideHUD];
                if (success) {
                    if ([preSelectModel.birthday isEqualToString:[model birthday]] &&
                        preSelectModel.gender == model.gender &&
                        preSelectModel.baby_id != model.baby_id) {
                        [[NSNotificationCenter defaultCenter] postNotificationName:@"kSYUserBabyInfoChangedNotification" object:[NSDictionary dictionary]];
                    }
                    
                    [self bbjDeleteBabyActionDo_sucess];

                } else {
                    if (![IMYNetState networkEnable]) {
                        [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                    } else {
                        [UIWindow imy_showTextHUD:IMYString(@"加载失败，请重试")];
                    }
                }
            }];
        } else {//关注宝宝删除 --bbj
            [self deleteBabyWithBabyId:self.babyModel.bbj_baby_id completion:^(id  _Nullable resData, NSError * _Nullable error) {
                @strongify(self);
                [self.deleteButton showLoading:NO];
                self.deleteButton.userInteractionEnabled = NO;
                [UIWindow imy_hideHUD];
                if (error) {
                    if (![IMYNetState networkEnable]) {
                       [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                    } else {
                       [UIWindow imy_showTextHUD:IMYString(@"加载失败，请重试")];
                    }
                }
                if (![resData boolValue]) {
                    [self imy_showTextHUD:@"删除失败"];
                    return;
                }
                [[IMYURIManager shareURIManager] runActionWithString:@"startAutoRecordData/babyHelper"];
                //删除宝宝相册记录
                [BBJAlbumHelper removeAssetWithBabyId:self.babyModel.bbj_baby_id];
                BBJ_POST_NOTIFY(BBJNotification_deleteBaby, @(self.babyModel.bbj_baby_id));
                [self imy_pop:YES];
            }];
        }
    } else {
        self.babyModel.is_deleted = YES;
        [self.deleteButton showLoading:YES];
        self.deleteButton.userInteractionEnabled = YES;
        [UIWindow imy_showTextHUDWithoutUI];
        @weakify(self);
        [self syncBabyInfoToServerWithPosition:19 completion :^(BOOL success, NSString *errorMessage) {
            @strongify(self);
            [self.deleteButton showLoading:NO];
            self.deleteButton.userInteractionEnabled = NO;
            [UIWindow imy_hideHUD];
            if (success) {
                [self bbjDeleteBabyActionDo_sucess];
            } else {
                if (![IMYNetState networkEnable]) {
                    [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                } else {
                    [UIWindow imy_showTextHUD:IMYString(@"加载失败，请重试")];
                }
            }
        }];
    }
    
}

//宝宝记 删除接口
- (void)deleteBabyWithBabyId:(NSInteger)babyId completion:(BBJCompletionBlk)completion{
    @weakify(self);
    NSDictionary *param = @{@"baby_id": @(babyId)};
    [BBJServerRequest deletePath:@"baby" params:param completion:^(id  _Nullable data, NSError * _Nullable error) {
        @strongify(self);
        if (completion) {
            completion(data,error);
        }
    }];
}

- (void)bbjDeleteBabyActionDo_sucess {
    ///通知宝宝记模块,悬浮窗需要监听数据
    [NSNotificationCenter.defaultCenter postNotificationName:@"BBJNotification_removeFloating" object:@(self.babyModel.baby_id)];
    NSString *babyId = [NSString stringWithFormat:@"%@",self.babyModel.baby_id ? @(self.babyModel.baby_id) : @"null"];
    [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"delete_baby", @"action": @(2), @"common_baby_id": babyId} headers:nil completed:nil];

    for (UIViewController *vc in self.navigationController.viewControllers) {
        if ([vc isKindOfClass:[SYBabyListVC class]]) {
            [self.navigationController popToViewController:vc animated:YES];
            return;
        }
    }
    
    [self imy_pop:YES];
}
- (void)iconAction:(BOOL)isAvatar {
    [self.view endEditing:YES];
    NSArray *actions = @[ IMYString(@"拍照"),IMYString(@"从手机相册选择")];
    
    if (!isAvatar) {
        actions = @[@"拍照", @"从相册选择", @"美柚图库"];
        //只更新 背景
        self.updateImageviewType = 2;
        //选择面板 埋点
        [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_ghbj_xxmb",
                                                          @"action": @(1)
                                                       } headers:nil completed:nil];
    }else {
        //只更新 头像
        self.updateImageviewType = 1;
    }
    if (self.saveImmediately && ![IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        return;
    }
    @weakify(self);
    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                             otherTitles:actions
                                 summary:nil
                              showInView:self.navigationController.view
                                  action:^(NSInteger index) {
        @strongify(self);
        if (!isAvatar) {
            if (index == 1) {//拍照
                [self photograph:NO];
                [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_ghbj_xxmb",
                                                                  @"action": @(2),
                                                                  @"public_type": @(1)
                                                               } headers:nil completed:nil];
                self.selectedPhotoTypeForBI = 1;
            } else if (index == 2) {//相册
                [self photoPicker:NO];
                [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_ghbj_xxmb",
                                                                  @"action": @(2),
                                                                  @"public_type": @(2)
                                                               } headers:nil completed:nil];
                self.selectedPhotoTypeForBI = 2;
            } else if (index == 3) {
                [self handleResetCoverImage];
                [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_ghbj_xxmb",
                                                                  @"action": @(2),
                                                                  @"public_type": @(5)
                                                               } headers:nil completed:nil];
                self.selectedPhotoTypeForBI = 5;
            }else if (index == 0) {
                [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_ghbj_xxmb",
                                                                  @"action": @(2),
                                                                  @"public_type": @(4)
                                                               } headers:nil completed:nil];
            }
        }else {
            if (index == 1) {//拍照
                [self photograph:YES];
            } else if (index == 2) {//相册
                [self photoPicker:YES];
            }
        }
       
    }];
}

//拍照2 宝宝记自定义相机
- (void)photograph:(BOOL)isAvatar  {
    @weakify(self);
    [BBJUtil requesCameraAuthorityWithType:isAvatar?BBJAlbumNoAccessViewTypeTakeAvatar:BBJAlbumNoAccessViewTypeTakeCover callback:^(BOOL allow) {
        if (!allow) {
            return ;
        }
        
        BBJCustomCameraVC *cameraVC = [[BBJCustomCameraVC alloc] init];
        @strongify(self);
        [cameraVC setFinishActionBlock:^(UIImage * _Nonnull image) {
            @strongify(self);
            [self reloadUpImage:image];
        }];
        IMYPublicBaseNavigationController *nav = [[IMYPublicBaseNavigationController alloc] initWithRootViewController:cameraVC];
        [self presentViewController:nav animated:YES completion:nil];
        
        return;
    }];
}
//点击相册(881前 系统相册查看照片 无法偏移,更换宝宝记自定义相机)
- (void)photoPicker:(BOOL)isAvatar {
    if (isAvatar) {
        [IMYEventHelper event:@"tjtp" label:IMYString(@"系统相册")];

        IMYAssetPickerController *vc = [[IMYAssetPickerController alloc] init];
        vc.styleType = IMYAssetPickerUITypeSingle;
        vc.delegate = self;
        vc.ratioSize = CGSizeMake(SCREEN_WIDTH, SCREEN_HEIGHT/2);
        [self imy_present:vc animated:YES];
    } else {
        [self gotoPhotoPickerView:isAvatar];
    }
}

- (void)gotoPhotoPickerView:(BOOL)isAvatar  {
    @weakify(self);
    [BBJUtil requestPhotoAuthorityWithType:isAvatar?BBJAlbumNoAccessViewTypeChooseAvatar:BBJAlbumNoAccessViewTypeChooseCover callback:^(BOOL allow) {
        if (!allow) {
            return ;
        }
        @strongify(self);
        //881 云相册需求:当前宝宝云相册没有照片,跳转到本地相册,所以需要请求当前宝宝picCount
        [IMYBabyMVTemplateViewModel requestBabyPicCountDataWithBabyId:self.babyModel.bbj_baby_id completeBlock:^(BOOL bSuccess, NSInteger picCount, NSString * _Nonnull header, NSString * _Nonnull nickname, NSString *birthday) {
            @strongify(self);
            BBJPhotoPickerConfig *config = [[BBJPhotoPickerConfig alloc] init];
            config.showType = BBJPhotoShowTypeImage;
            config.allowCloudAlbum = YES;//是否云相册
            config.distinguishTime = YES;//时间轴
            config.curUseForCover = YES;//更换宝宝背景标识
            config.babyId = self.babyModel.bbj_baby_id;
            config.babyHeader = self.babyModel.avatar;
            config.babyNickName = self.babyModel.nickname;
            config.cloudPhotoCount = picCount;
            
            BBJPhotoPickerViewController *vc = [[BBJPhotoPickerViewController alloc] initWithBabyId:self.babyModel.bbj_baby_id config:config];
            //是否是图片单选
            vc.forAvatarEdit = YES;
            vc.selectedPhoto = ^(UIImage * _Nonnull image) {
                @strongify(self);
                [self reloadUpImage:image];
            };
            BBJNavigationController *nav = [[BBJNavigationController alloc] initWithRootViewController:vc];
            [nav.navigationBar setHidden:YES];
            [self presentViewController:nav animated:YES completion:nil];
        }];
       
    }];
}

//881 默认背景图 修改为 美柚图库
- (void)handleResetCoverImage {
    BBJMeiYouPhotoLibrayViewController *vc = [[BBJMeiYouPhotoLibrayViewController alloc] init];
    @weakify(self);
    vc.selectedPhoto = ^(UIImage * _Nonnull image) {
        @strongify(self);
        [self reloadUpImage:image];
    };
    BBJNavigationController *nav = [[BBJNavigationController alloc] initWithRootViewController:vc];
    [nav.navigationBar setHidden:YES];
    [self presentViewController:nav animated:YES completion:nil];
}
#pragma mark IMYAssetPickerControllerDelegate
- (void)imy_topLeftButtonTouchupInside {
    [super imy_topLeftButtonTouchupInside];
}

-(void)assetPickerController:(IMYAssetPickerController *)assetPickerController didCropImage:(UIImage *)image{
    [self reloadUpImage:image];
}


- (void)reloadUpImage:(UIImage *)image{
    if (![IMYNetState networkEnable] && !self.isAddBaby) {
        [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
        return;
    }
    if (self.updateImageviewType == 2) {
        CGSize imageSize = [image imy_pixelSize];
        if (imageSize.width < 320 || imageSize.height < 320) {
            [self imy_showTextHUD:@"您上传的尺寸太小，请重新上传"];
            return;
        }
        self.cover = [image imy_scaledImageWithMaxSize:imageSize];
        self.isBabyBGUpload = NO;
        if (self.saveBabyBGImmediately) {
            [UIWindow imy_showLoadingHUDWithText:IMYString(@"保存中")];
            @weakify(self);
            [self uploadBabyBG:^(BOOL success, NSString *url, NSString *errorMessage) {
                @strongify(self);
                if (success && url && imy_isNotEmptyString(url)) {
                    [self syncBabyCoverToServerWithUrlString:url Completion:^(BOOL success, NSString *errorMessage) {
                        imy_asyncMainBlock(0.2, ^{
                            @strongify(self);
                            if (success) {
                                //宝宝背景上传成功，并同步服务器
                                [UIWindow imy_showTextHUDWithDelay:2 image:[UIImage imy_imageForKey:@"toast_icon_yes"] text:IMYString(@"保存成功")];
                                
                                if (self.selectedPhotoTypeForBI > 0) {
                                    //换背景成功 埋点
                                    [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_ghbj_ghcg",
                                                                                      @"action": @(2),
                                                                                      @"public_type": @(self.selectedPhotoTypeForBI)
                                                                                   } headers:nil completed:nil];
                                }
                                
                                [self updateView];
                                //881弹出 :引导上传弹窗 当天展示过后 会返回nil
                                BBJBabyCoverPhotoNewFeatureAlert *aler =  [BBJBabyCoverPhotoNewFeatureAlert showSheetViewWithCoverImage:image];
                                if (aler) {
                                    aler.babyID = self.babyModel.bbj_baby_id;
                                    aler.common_baby_id = self.babyModel.baby_id;
                                    aler.babyType = 0;
                                }
                                
                            }else {
                                [self upImageFailHudIsAvatar:NO];
                            }
                        });
                    }];
                } else {
                    [self upImageFailHudIsAvatar:NO ];
                }
            }];
        } else {
            [self updateView];
        }
    }else {
        self.avatar = [image imy_scaledImageWithMaxSize:CGSizeMake(720, 720)];
        self.isAvatarUpload = NO;
        if (self.saveImmediately) {
            [UIWindow imy_showLoadingHUDWithText:IMYString(@"保存中")];
            @weakify(self);
            [self uploadAvatar:^(BOOL success, NSString *url, NSString *errorMessage) {
                @strongify(self);
                if (success && url && imy_isNotEmptyString(url)) {
                    //宝宝头像上传成功，发个通知吧
                    NSString *url_old = self.babyModel.avatar;
                    self.babyModel.avatar = url;
                    if (self.babyModel.is_owner) {
                        //孕育身份修改头像接口
                        [self syncBabyInfoToServerWithPosition:18 completion:^(BOOL success, NSString *errorMessage) {
                            @strongify(self);
                            imy_asyncMainBlock(0.2, ^{
                                if (success) {
                                    [[NSNotificationCenter defaultCenter] postNotificationName:kSYUserChangeBabyDisplayChangedNotification object:nil];
                                    //宝宝头像更改
                                    NSDictionary *dic = @{@"baby_id": @(self.babyModel.bbj_baby_id), @"header": self.babyModel.avatar};
                                    BBJ_POST_NOTIFY(BBJNotification_updateBabyAvatar, dic);
                                    self.hasSyncBabyInfo = YES;
                                    [self.babyModel updateToDB];
                                    [self updateView];
                                    
                                    [UIWindow imy_showTextHUDWithDelay:2 image:[UIImage imy_imageForKey:@"toast_icon_yes"] text:IMYString(@"保存成功")];
                                }else{
                                    self.babyModel.avatar = url_old;
                                    [self upImageFailHudIsAvatar:YES];
                                }
                            });
                        }];
                    } else {//关注宝宝身份修改头像接口--bbj
                        NSMutableDictionary *param = [NSMutableDictionary new];
                        [param setObject:url forKey:@"header"];
                        
                        [self updateBabyInfoWithParamter:param babyId:self.babyModel.bbj_baby_id completion:^(id  _Nullable resData, NSError * _Nullable error) {
                            imy_asyncMainBlock(0.2, ^{
                                @strongify(self);
                                if (error) {
                                    self.babyModel.avatar = url_old;
                                    [self upImageFailHudIsAvatar:YES];
                                    return;
                                }
                                [[NSNotificationCenter defaultCenter] postNotificationName:kSYUserChangeBabyDisplayChangedNotification object:nil];
                                //宝宝头像更改
                                NSDictionary *dic = @{@"baby_id": @(self.babyModel.bbj_baby_id), @"header": url};
                                BBJ_POST_NOTIFY(BBJNotification_updateBabyAvatar, dic);
                                self.hasSyncBabyInfo = YES;
                                self.babyModel.avatar = url;
                                [self.babyModel updateToDB];
                                [self updateView];
                                [UIWindow imy_showTextHUDWithDelay:2 image:[UIImage imy_imageForKey:@"toast_icon_yes"] text:IMYString(@"保存成功")];
                            });
                        }];
                    }
                } else {
                    [self upImageFailHudIsAvatar:YES];
                }
            }];
        } else {
            [self updateView];
        }
    }
}
- (void)upImageFailHudIsAvatar:(BOOL)isAvatar {
    if (isAvatar) {
        self.avatar = nil;
    }else {
        self.cover = nil;
    }
    [UIWindow imy_hideHUD];
    if ([IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:IMYString(@"保存失败")];
    } else {
        [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
    }
}

#pragma mark - UITableViewDelegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 0) {
        return self.dataSource.count;
    } else if (section == 1) {
        return 1;
    }else {
        return 1; //删除按钮
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 8;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 1) {
        if ([self isHomePageDisplay]) {
            return 48;
        } else {
            return 0;
        }
        
    } else {
        return 48;
    }
}
//是否显示  首页展示  cell 884
- (BOOL)isHomePageDisplay {
   /*
    
    用户身份=怀孕、辣妈、备孕时，宝宝资料页新增“首页展示”开关
    是关注的宝宝
    
    已出生的宝宝(胎宝宝 不能关注)
    */
    if (([IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModeNormal) && self.babyModel.is_owner == NO) {
        return YES;
    }
    
    return NO;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if(indexPath.section == 0) {
        
        SYTextTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"SYTextTableViewCell" forIndexPath:indexPath];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        [cell.cardView imy_showLineForRow:indexPath.row leftMargin:12 rowCount:self.dataSource.count];
        SYTextTableViewCellModel *model = self.dataSource[indexPath.row];
        if (model.configureHandler) {
            model.configureHandler(cell);
        }
        [cell showCardStyle];
        if (indexPath.row == 0) {
            [cell drawTopCornerRadius];
        } else if (indexPath.row == self.dataSource.count-1) {
            [cell drawBottomCornerRadius];
        }
        //关注宝宝:cell箭头 有权限判断
        //我是宝宝的 可修改
        if (model.cellType != SYBabyInfoRelative) {
            if (self.isAddBaby && model.cellType == SYBabyInfoNikname) {
                cell.showFlagView = NO;
            } else if (!self.babyModel.is_owner && !self.alter_baby) {
                cell.showFlagView = NO;
            } else {
                cell.showFlagView = YES;
            }
        }else{
            cell.showFlagView = YES;
        }
        return cell;

    } else  if(indexPath.section == 1) {
        SYSwitchShowTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(SYSwitchShowTableViewCell.class) forIndexPath:indexPath];
        [cell setModel:self.switchMode];
        [cell drawCardStyle:indexPath rowCount:1];
        @weakify(self);
        cell.switchBtn.onDidStateChanged = ^(IMYSwitch *swi, BOOL isON) {
            @strongify(self);
            NSString *showMessage = self.babyModel.nickname;
            NSInteger number ;
            if (isON) {
                number = 1;
            } else {
                number = 2;
            }
            [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_bbxx_syzskg", @"action": @(2), @"pageName": @"SYBabyInfoVC",@"public_type": isON ? @"关闭":@"打开"} headers:nil completed:nil];
            [self syncBabyDisplayToHomepageWithBabyID:self.babyModel.bbj_baby_id isDisplay:number Completion:^(BOOL success, NSString *errorMessage) {
                if (success) {
                    if (isON) {
                        [UIWindow imy_showTextHUD:[NSString stringWithFormat:@"可以在首页查看%@",showMessage]];
                    } else {
                        [UIWindow imy_showTextHUD:[NSString stringWithFormat:@"已不在首页展示%@的记录",showMessage]];
                    }
                    //通知首页刷新tab个数
                    imy_asyncBlock(1, ^{
                        [[NSNotificationCenter defaultCenter] postNotificationName:kSYUserChangeBabyDisplayChangedNotification object:nil];
                    });
                    
                } else {
//                    [UIWindow imy_showTextHUD:[NSString stringWithFormat:@"关注宝宝接口报错"]];
                    NSLog(@"关注宝宝 是否首页展示  请求接口报错!!!!");
                }
            }];
        };
        
        //BI，曝光
        cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"SYSwitchShowTableViewCell_switchBtn"];
        cell.imyut_eventInfo.showRadius = 1.0;
        cell.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_bbxx_syzskg", @"action": @(1), @"pageName": @"SYBabyInfoVC",@"public_type": self.switchMode.isOn ? @"打开":@"关闭"} headers:nil completed:nil];
        };
        
        return cell;
    } else {
        // 底部删除按钮
        SYDeleteTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(SYDeleteTableViewCell.class) forIndexPath:indexPath];
        if (self.babyModel.is_owner) {
            [cell setCellDeleteButtonTitleWith:SYBabyInfoCellModelTypeDelete];
        } else {
            [cell setCellDeleteButtonTitleWith:SYBabyInfoCellModelTypetQuit];
        }
        self.deleteButton = cell.loadingButton;
        //埋点
        @weakify(self)
        //BI，曝光
        cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"SYDeleteTableViewCell_BBJBabyInfoCellModelTypetQuit"];
        cell.imyut_eventInfo.showRadius = 1.0;
        cell.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            NSString *babyId = [NSString stringWithFormat:@"%@",self.babyModel.baby_id ? @(self.babyModel.baby_id) : @"null"];
            [IMYGAEventHelper postWithPath:@"event" params:@{ @"event":self.babyModel.is_owner? @"delete_baby":@"yy_bbxxy_tcqyt", @"action": @(1), @"common_baby_id": babyId} headers:nil completed:nil];
        };
        return cell;
        
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        SYTextTableViewCellModel *model = self.dataSource[indexPath.row];
        //关系 可点击,其他cell 需要权限(关注宝宝的)
        if (model.cellType != SYBabyInfoRelative) {
            if (!self.babyModel.is_owner && !self.isAddBaby) {
                if (!self.alter_baby) {
                    return;
                }
            }
        }
         if (model.selectHandler) {
             model.selectHandler();
         }
    } else if(indexPath.section == 2) {
        if (self.babyModel.is_owner) {//自己宝宝是删除
            // 删除操作
            [self deleteAction];
        }else {//关注宝宝是退出亲友团
            [self handleQuitAction];
        }
        
    }
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 3;
}

#pragma mark - 获取(删除)宝宝的相关亲友关系/权限
- (void)reqBBJBabyInfo {
    if (self.isAddBaby || !self.babyModel) {
        self.babyFollowerCount = 0;
        return;
    }
    if (self.babyModel.baby_id == 0) {
        self.babyFollowerCount = 0;
        return;
    }
    
    @weakify(self);
    [[IMYServerRequest getPath:@"v2/baby/follower" host:api_bbj_meiyou_com params:@{@"common_baby_id":@(self.babyModel.baby_id)} headers:nil] subscribeNext:^(IMYHTTPResponse *x) {
        @strongify(self);
        imy_asyncMainBlock(^{
            NSDictionary *dic = x.responseObject;
            if ([dic isKindOfClass:[NSDictionary class]]) {
                self.babyFollowerCount = [dic[@"follower_count"] intValue];
                // 处理 baby_center_uri（关键修复点）
                id uriObj = dic[@"baby_center_uri"];
                if ([uriObj isKindOfClass:[NSString class]]) {
                    self.babyFollowerUri = uriObj;
                } else {
                    // 若类型不匹配，赋值为空字符串或 nil（根据业务需求）
                    self.babyFollowerUri = nil;
                }
            } else {
                self.babyFollowerCount = -1;
                self.babyFollowerUri = nil;
            }
            if (!self.cover) {
                NSString *cover = dic[@"baby_info"][@"cover"];
                if ([cover isKindOfClass:[NSString class]]) {
                    [BBJBabyCacheManager updateBabyCover:cover commonBabyId:self.babyModel.baby_id];
                    [self updateBabyCoverImageView];
                }
            }
            //是否有权限修改赋值
            self.alter_baby = [dic[@"alter_baby"] boolValue];
            if (!self.babyModel.is_owner) {//关注宝宝权限判断
                [self changeUIConstraintsWithAlterRight:self.alter_baby];
            }

        });
    } error:^(NSError *error) {
        @strongify(self);
        self.babyFollowerCount = -1;
        self.babyFollowerUri = nil;
    }];
}

#pragma mark - 获取要删除的宝宝的最后一次记录
- (void)reqBBJBabyLastRecord{
    if (self.isAddBaby || !self.babyModel) {
        self.hasLastRecord = NO;
        self.lastRecord = nil;
        return;
    }
    if (self.babyModel.baby_id == 0) {
        self.hasLastRecord = NO;
        self.lastRecord = nil;
        return;
    }
    
    @weakify(self);
    [[IMYServerRequest getPath:@"v3/baby/last/record" host:api_bbj_meiyou_com params:@{@"common_baby_id":@(self.babyModel.baby_id)} headers:nil] subscribeNext:^(IMYHTTPResponse *x) {
        @strongify(self);
        NSDictionary *dic = x.responseObject;
        if ([dic isKindOfClass:[NSDictionary class]]) {
            self.hasLastRecord = [dic[@"had"] boolValue];
            self.lastRecord = dic[@"content"];
        } else {
            self.hasLastRecord = NO;
            self.lastRecord = nil;
        }
    } error:^(NSError *error) {
        @strongify(self);
        self.hasLastRecord = NO;
        self.lastRecord = nil;
    }];
}

- (void)changeUIConstraintsWithAlterRight:(BOOL)isRight {
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        if (isRight == NO) {//请求无权限  无法编辑头像/列表
            self.avatarImageView.userInteractionEnabled = NO;
            [self.iconCameraFlag setHidden:YES];
            [self.babyBGImageView setHidden:NO];
            [self.changeBGButtonMaskView setHidden:YES];
            [self.changeBGButton setHidden:YES];
            [self.coverBgMaskView setHidden:NO];
            [self.babyBGImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.edges.mas_equalTo(UIEdgeInsetsZero);
            }];
            [self.coverBgMaskView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.edges.mas_equalTo(UIEdgeInsetsZero);
            }];
            
            [self.avatarBgView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.size.mas_equalTo(CGSizeMake(72+3, 72+ 3));
                make.centerY.mas_equalTo(self.headerView);
                make.left.equalTo(self.headerView).offset(36);
            }];
            
            [self.avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.center.equalTo(self.avatarBgView);
                make.width.height.mas_equalTo(72);
            }];
        }else{//有权限 编辑头像/刷新tableview
            self.avatarImageView.userInteractionEnabled = YES;
            [self.iconCameraFlag setHidden:NO];
            [self.babyBGImageView setHidden:NO];
            [self.changeBGButtonMaskView setHidden:NO];
            [self.changeBGButton setHidden:NO];
            [self.coverBgMaskView setHidden:NO];
            
            [self.babyBGImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.edges.mas_equalTo(UIEdgeInsetsZero);
            }];
            [self.coverBgMaskView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.edges.mas_equalTo(UIEdgeInsetsZero);
            }];
            [self.avatarBgView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.size.mas_equalTo(CGSizeMake(72+3, 72+ 3));
                make.centerY.mas_equalTo(self.headerView);
                make.left.equalTo(self.headerView).offset(36);
            }];
            
            [self.avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.center.equalTo(self.avatarBgView);
                make.width.height.mas_equalTo(72);
            }];
            
            [self.iconCameraFlag mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.size.mas_equalTo(CGSizeMake(23, 23));
                make.right.equalTo(self.avatarImageView.mas_right).offset(0);
                make.bottom.equalTo(self.avatarImageView.mas_bottom).offset(0);
            }];
            
            [self.changeBGButtonMaskView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.size.mas_equalTo(CGSizeMake(71, 24));
                make.top.equalTo(self.headerView).mas_offset(20);
                make.right.equalTo(self.headerView.mas_right).mas_offset(-24);
            }];
            [self.changeBGButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.edges.mas_equalTo(self.changeBGButtonMaskView);
            }];
        }
        //刷新目的是 列表tableviewcell 右边有箭头显示
        [self.tableView reloadData];
    });
}

//亲友退出宝宝相册
- (void)handleQuitAction {
    BBJBabyListModel *bbjBabyListModel = [IMYRecordBabyModel convetIMYBabyModeToBBJRecordBaby:self.babyModel];
    [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_bbxxy_tcqyttc",
                                                      @"action": @(1),
                                                      @"common_baby_id": @(bbjBabyListModel.common_baby_id),
                                                   } headers:nil completed:nil];
    NSString *msg = @"退出亲友团后，将无法继续查看宝宝记录，确定要退出吗？";
    @weakify(self);
    [IMYActionMessageBox showBoxWithTitle:nil
                                  message:msg
                                    style:IMYMessageBoxStyleFlat
                        isShowCloseButton:NO
                            textAlignment:NSTextAlignmentCenter
                        cancelButtonTitle: IMYString(@"取消")
                         otherButtonTitle: IMYString(@"退出")
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        @strongify(self);
        if (sender == messageBox.rightButton) {
            [self imy_showLoadingHUD];
            [messageBox dismiss];
            [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_bbxxy_tcqyttc",
                                                              @"action": @(2),
                                                              @"common_baby_id": @(bbjBabyListModel.common_baby_id),
                                                              @"index": @(2)
                                                           } headers:nil completed:nil];

            
            NSDictionary *param = @{@"baby_id": @(self.babyModel.bbj_baby_id)};
            [BBJServerRequest deletePath:@"follow" params:param completion:^(id  _Nullable data, NSError * _Nullable error) {
                @strongify(self);
                if (error) {
                    imy_asyncMainBlock(^{
                        [self imy_hideHUD];
                        [self bbj_showErrorMessage:error];
                    });
                    return ;
                }else{
                    [[BBJBabyCacheManager shareInstance] getQinYouNetWorkBabyListIgnoreLoginWithCompletion:^(id  _Nullable resData, NSError * _Nullable error) {
                        imy_asyncMainBlock(^{
                            [self imy_hideHUD];
                            if (!error) {
                                BBJ_POST_NOTIFY(BBJNotification_deleteBaby, @(self.babyModel.bbj_baby_id));
                                if (BBJBabyCacheManager.shareInstance.currentBabyId == self.babyModel.bbj_baby_id) {
                                    BBJBabyCacheManager.shareInstance.currentBabyId = 0;
                                }
                                [self imy_pop:YES];
                            }
                        });
                   
                    }];
                }
            }];

        } else if (sender == messageBox.leftButton) {
            [messageBox dismiss];
            [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"yy_bbxxy_tcqyttc",
                                                              @"action": @(2),
                                                              @"common_baby_id": @(bbjBabyListModel.common_baby_id),
                                                              @"index": @(1)
                                                           } headers:nil completed:nil];
        }
    }];
}

@end
